# Nginx Dual Routing Setup Guide for Laravel + ImpressCMS Legacy

This guide will help you configure nginx to properly handle the dual routing system where <PERSON><PERSON> serves as the primary entry point with fallback to legacy ImpressCMS files.

## 🎯 **Problem Summary**

The issue you encountered ("No input file specified") occurs because:
1. <PERSON>in<PERSON> doesn't process `.htaccess` files (unlike Apache)
2. The `fastcgi_param SCRIPT_FILENAME` wasn't correctly resolving to legacy PHP files
3. The working directory context wasn't properly set for legacy file execution

## 🔧 **Solution Overview**

We provide three nginx configurations with different approaches:

### 1. **nginx-production.conf** (Recommended)
- Direct nginx routing for legacy PHP files (best performance)
- <PERSON><PERSON> fallback for non-existent files
- Proper FastCGI parameter setup for legacy context

### 2. **nginx-simple.conf** (Simplest)
- Routes everything through Laravel
- Laravel middleware handles all dual routing logic
- Easier to debug but slightly more overhead

## 📋 **Step-by-Step Setup**

### Step 1: Choose and Configure Nginx Virtual Host

1. **Copy the recommended configuration:**
```bash
sudo cp nginx-production.conf /etc/nginx/sites-available/imlara.test
```

2. **Edit the configuration file:**
```bash
sudo nano /etc/nginx/sites-available/imlara.test
```

3. **Update the root path:**
```nginx
# Change this line to your actual project path
root /path/to/your/imlara/project;
```

4. **Update PHP-FPM socket path if needed:**
```nginx
# Change php8.3-fpm.sock to your PHP version
fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
```

### Step 2: Enable the Site

1. **Create symlink:**
```bash
sudo ln -s /etc/nginx/sites-available/imlara.test /etc/nginx/sites-enabled/
```

2. **Add to hosts file:**
```bash
echo "127.0.0.1 imlara.test" | sudo tee -a /etc/hosts
```

### Step 3: Test and Reload

1. **Test nginx configuration:**
```bash
sudo nginx -t
```

2. **Reload nginx:**
```bash
sudo systemctl reload nginx
```

### Step 4: Verify Setup

1. **Run the test script:**
```bash
php test-nginx-routing.php
```

2. **Test in browser:**
- Visit `http://imlara.test` - should redirect to installation
- Visit `http://imlara.test/user.php` - should show ImpressCMS installation
- Visit `http://imlara.test/laravel-test` - should show Laravel JSON response

## 🔍 **How It Works**

### Request Flow:

1. **Static Assets**: `nginx` checks `/legacy/` first, then `/public/`
2. **PHP Files**: 
   - If file exists in `/legacy/` → serve directly with legacy context
   - If not → route to Laravel's `public/index.php`
3. **Laravel Middleware**: Handles any complex routing logic as fallback

### Key Nginx Configuration Elements:

```nginx
# Check if legacy PHP file exists
location ~ \.php$ {
    set $legacy_file $document_root/legacy$uri;

    if (-f $legacy_file) {
        # Set proper FastCGI params for legacy context
        fastcgi_param SCRIPT_FILENAME $legacy_file;
        fastcgi_param DOCUMENT_ROOT $document_root/legacy;
        fastcgi_param PWD $document_root/legacy;
        # ... other params
    }
}
```

## 🚨 **Troubleshooting**

### "No input file specified" Error

**Cause**: `SCRIPT_FILENAME` is not pointing to the correct file.

**Solutions**:
1. Check that the file path in nginx config is correct
2. Verify file permissions: `ls -la /path/to/legacy/user.php`
3. Check nginx error logs: `tail -f /var/log/nginx/imlara.test.error.log`

### Redirects Not Working

**Cause**: Legacy files send `header('Location: ...')` but nginx doesn't handle it properly.

**Solutions**:
1. Use the enhanced Laravel middleware (already implemented)
2. Check that `fastcgi_param REQUEST_URI` is set correctly
3. Verify the redirect URL in browser developer tools

### Static Assets Not Loading

**Cause**: CSS/JS files not found in either legacy or public directories.

**Solutions**:
1. Check the `try_files` order in nginx config
2. Verify file paths: `ls -la /path/to/legacy/css/` and `ls -la /path/to/public/css/`
3. Check browser network tab for 404 errors

### Laravel Routes Not Working

**Cause**: Requests not reaching Laravel's front controller.

**Solutions**:
1. Verify `try_files` includes `/public/index.php?$query_string`
2. Check Laravel logs: `tail -f storage/logs/laravel.log`
3. Test Laravel directly: `http://imlara.test/public/index.php/laravel-test`

## 📊 **Testing Checklist**

- [ ] `http://imlara.test` redirects to installation
- [ ] `http://imlara.test/user.php` shows ImpressCMS content
- [ ] `http://imlara.test/test-legacy.php` returns JSON
- [ ] `http://imlara.test/laravel-test` returns Laravel JSON
- [ ] `http://imlara.test/non-existent` shows Laravel 404
- [ ] Static assets load correctly (CSS, JS, images)

## 🔄 **Fallback to Apache**

If you need to use Apache instead of nginx, the existing `.htaccess` file should work without changes:

```bash
# Enable Apache modules
sudo a2enmod rewrite
sudo systemctl restart apache2
```

The `.htaccess` file already contains the correct dual routing rules for Apache.

## 📞 **Support**

If you encounter issues:

1. **Check nginx error logs**: `tail -f /var/log/nginx/error.log`
2. **Check PHP-FPM logs**: `tail -f /var/log/php8.3-fpm.log`
3. **Test with curl**: `curl -v http://imlara.test/user.php`
4. **Verify file permissions**: `namei -om /path/to/legacy/user.php`

The dual routing system should now work identically to how it worked with the PHP development server!
