<?php

use Illuminate\Support\Facades\Route;

// Define your Laravel routes first
Route::get('/laravel-test', function () {
    return response()->json([
        'message' => 'Laravel routing is working',
        'timestamp' => now(),
        'environment' => app()->environment()
    ]);
});

Route::get('/debug-legacy', function () {
    $path = request()->getPathInfo();
    $legacyFilePath = base_path('legacy' . $path);

    // Test specific problematic paths
    $testPaths = [
        '/install/',
        '/install/index.php',
        '/test-legacy.php',
        '/admin.php',
        '/index.php'
    ];

    $pathTests = [];
    foreach ($testPaths as $testPath) {
        $testFilePath = base_path('legacy' . $testPath);
        $pathTests[$testPath] = [
            'full_path' => $testFilePath,
            'exists' => file_exists($testFilePath),
            'is_file' => is_file($testFilePath),
            'is_dir' => is_dir($testFilePath),
            'is_readable' => is_readable($testFilePath),
        ];

        // If it's a directory, check for index.php
        if (is_dir($testFilePath)) {
            $indexPath = rtrim($testFilePath, '/') . '/index.php';
            $pathTests[$testPath]['index_php'] = [
                'path' => $indexPath,
                'exists' => file_exists($indexPath),
                'is_readable' => is_readable($indexPath)
            ];
        }
    }

    return response()->json([
        'current_request' => [
            'path' => $path,
            'legacy_file_path' => $legacyFilePath,
            'legacy_file_exists' => file_exists($legacyFilePath),
            'is_file' => is_file($legacyFilePath),
            'is_dir' => is_dir($legacyFilePath),
            'is_readable' => is_readable($legacyFilePath),
        ],
        'path_tests' => $pathTests,
        'base_path' => base_path(),
        'legacy_base' => base_path('legacy'),
        'laravel_routes_working' => true
    ]);
});

// Add other Laravel routes here...

// Catch-all route for legacy fallback (must be last)
// The LegacyFallback middleware is now applied globally to all web routes
Route::fallback(function () {
    abort(404);
});
