<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // Add legacy fallback middleware as GLOBAL middleware to run before route matching
        // This ensures it intercepts requests before <PERSON><PERSON>'s routing and CSRF protection
        $middleware->use([
            \App\Http\Middleware\LegacyFallback::class,
        ]);

        // Also register as alias for route usage
        $middleware->alias([
            'legacy' => \App\Http\Middleware\LegacyFallback::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
