# .htaccess for Laravel + Legacy ImpressCMS Dual Routing
# This serves as backup if lighttpd supports .htaccess or for Apache compatibility

<IfModule mod_rewrite.c>
    RewriteEngine On

    # Security: Block access to sensitive files
    RewriteRule ^(\.env|\.git|composer\.(json|lock)|artisan)$ - [F,L]

    # Block access to Laravel internal directories
    RewriteRule ^(app|bootstrap|config|database|resources|routes|storage|tests|vendor)/ - [F,L]

    # Allow direct access to static assets
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule ^(css|js|images|fonts|favicon\.ico|robots\.txt) - [L]

    # Legacy admin files - check if they exist physically first
    RewriteCond %{REQUEST_URI} ^/modules/system/admin/(.*)$
    RewriteCond %{DOCUMENT_ROOT}/legacy/modules/system/admin/%1 -f
    RewriteRule ^modules/system/admin/(.*)$ /legacy/modules/system/admin/$1 [L]

    # Legacy user authentication files
    RewriteCond %{REQUEST_URI} ^/(user\.php|admin\.php)
    RewriteCond %{DOCUMENT_ROOT}/legacy/$1 -f
    RewriteRule ^(user\.php|admin\.php)(.*)$ /legacy/$1$2 [L]

    # Legacy module files - check if they exist physically first
    RewriteCond %{REQUEST_URI} ^/modules/(.*)$
    RewriteCond %{DOCUMENT_ROOT}/legacy/modules/%1 -f
    RewriteRule ^modules/(.*)$ /legacy/modules/$1 [L]

    # Legacy files in general - direct access if they exist
    RewriteCond %{REQUEST_URI} ^/legacy/(.*)$
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule ^legacy/(.*)$ - [L]

    # Handle Angular/Vue.js routes (if you have frontend routes)
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} !^/legacy/
    RewriteRule ^(.*)$ /public/index.php [QSA,L]
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache control for static assets
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
</IfModule>

# PHP settings (if allowed)
<IfModule mod_php.c>
    php_value upload_max_filesize 64M
    php_value post_max_size 64M
    php_value max_execution_time 300
    php_value max_input_vars 3000
    php_value memory_limit 256M
</IfModule>
