# lighttpd Configuration for Laravel + Legacy ImpressCMS
# This configuration handles dual routing: <PERSON><PERSON> primary, legacy fallback

server.modules = (
    "mod_access",
    "mod_alias",
    "mod_compress",
    "mod_redirect",
    "mod_rewrite",
    "mod_fastcgi",
    "mod_expire"
)

# Basic server settings
server.document-root = "/path/to/your/domain/public_html/public"
server.errorlog = "/path/to/your/domain/logs/error.log"
accesslog.filename = "/path/to/your/domain/logs/access.log"

# MIME types
mimetype.assign = (
    ".html" => "text/html",
    ".txt" => "text/plain",
    ".jpg" => "image/jpeg",
    ".png" => "image/png",
    ".css" => "text/css",
    ".js" => "application/javascript",
    ".php" => "application/x-httpd-php"
)

# PHP FastCGI configuration
fastcgi.server = ( ".php" =>
    ((
        "bin-path" => "/usr/bin/php-cgi",
        "socket" => "/tmp/php.socket",
        "max-procs" => 2,
        "idle-timeout" => 20,
        "bin-environment" => (
            "PHP_FCGI_CHILDREN" => "4",
            "PHP_FCGI_MAX_REQUESTS" => "10000"
        ),
        "bin-copy-environment" => (
            "PATH", "SHELL", "USER"
        )
    ))
)

# Security: Deny access to sensitive files
$HTTP["url"] =~ "^/(\.env|\.git|composer\.(json|lock)|artisan)" {
    url.access-deny = ( "" )
}

# Deny access to Laravel internal directories from web
$HTTP["url"] =~ "^/(app|bootstrap|config|database|resources|routes|storage|tests|vendor)/" {
    url.access-deny = ( "" )
}

# Allow access to legacy directory
$HTTP["url"] =~ "^/legacy/" {
    # Legacy files are allowed
}

# Main URL rewriting for dual routing system
url.rewrite-once = (
    # Static assets - serve directly
    "^/(css|js|images|fonts|favicon\.ico|robots\.txt)($|/)" => "$0",
    
    # Legacy files - check if they exist physically first
    "^/legacy/(.*)$" => "/legacy/$1",
    
    # Admin routes - check for legacy admin files first
    "^/(modules/system/admin/.*)$" => "/check-legacy-admin.php?path=$1",
    
    # User authentication routes
    "^/(user\.php|admin\.php)(.*)$" => "/legacy/$1$2",
    
    # Module routes - check legacy first
    "^/(modules/.*)$" => "/check-legacy-module.php?path=$1",
    
    # Everything else goes to Laravel
    "^/(.*)$" => "/index.php"
)

# Compression for better performance
compress.cache-dir = "/tmp/lighttpd/compress/"
compress.filetype = ( "application/javascript", "text/css", "text/html", "text/plain" )

# Expire headers for static content
expire.url = (
    "/css/" => "access plus 1 months",
    "/js/" => "access plus 1 months",
    "/images/" => "access plus 1 months",
    "/fonts/" => "access plus 1 months"
)

# Index files
index-file.names = ( "index.php", "index.html" )

# Directory listing (disable for security)
dir-listing.activate = "disable"
