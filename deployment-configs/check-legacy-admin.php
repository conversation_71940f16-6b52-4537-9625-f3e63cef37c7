<?php
/**
 * Legacy Admin File Checker for lighttpd
 * This script checks if legacy admin files exist and serves them, otherwise falls back to Laravel
 */

$requestPath = $_GET['path'] ?? '';
$legacyPath = __DIR__ . '/legacy/' . $requestPath;

// Security check - ensure path is within legacy directory
$realLegacyPath = realpath($legacyPath);
$realLegacyBase = realpath(__DIR__ . '/legacy/');

if ($realLegacyPath && $realLegacyBase && strpos($realLegacyPath, $realLegacyBase) === 0) {
    // Check if the legacy file exists
    if (file_exists($legacyPath)) {
        // If it's a PHP file, include it
        if (pathinfo($legacyPath, PATHINFO_EXTENSION) === 'php') {
            // Set up the legacy environment
            chdir(dirname($legacyPath));
            include $legacyPath;
            exit;
        } else {
            // For non-PHP files, serve them directly
            $mimeType = mime_content_type($legacyPath);
            header('Content-Type: ' . $mimeType);
            readfile($legacyPath);
            exit;
        }
    }
}

// If legacy file doesn't exist, fall back to <PERSON>vel
include __DIR__ . '/index.php';
?>
