#!/bin/bash
# File Permissions Setup Script for Laravel + Legacy ImpressCMS on Shared Hosting

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Setting up file permissions for Laravel + Legacy ImpressCMS...${NC}"

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    echo -e "${RED}Error: artisan file not found. Please run this script from the Laravel root directory.${NC}"
    exit 1
fi

# Laravel application directories and files
echo -e "${YELLOW}Setting Laravel application permissions...${NC}"

# Laravel directories - 755 (readable/executable by all, writable by owner)
find . -type d -exec chmod 755 {} \;

# Laravel files - 644 (readable by all, writable by owner)
find . -type f -exec chmod 644 {} \;

# Make artisan executable
chmod 755 artisan

# Storage and cache directories - 775 (group writable for web server)
echo -e "${YELLOW}Setting storage and cache permissions...${NC}"
chmod -R 775 storage/
chmod -R 775 bootstrap/cache/

# If using public directory structure
if [ -d "public" ]; then
    echo -e "${YELLOW}Setting public directory permissions...${NC}"
    chmod -R 755 public/
    
    # Legacy directory within public
    if [ -d "public/legacy" ]; then
        echo -e "${YELLOW}Setting legacy directory permissions...${NC}"
        chmod -R 755 public/legacy/
        
        # Legacy writable directories
        if [ -d "public/legacy/uploads" ]; then
            chmod -R 775 public/legacy/uploads/
        fi
        
        if [ -d "public/legacy/cache" ]; then
            chmod -R 775 public/legacy/cache/
        fi
        
        if [ -d "public/legacy/templates_c" ]; then
            chmod -R 775 public/legacy/templates_c/
        fi
    fi
fi

# If legacy is in separate directory
if [ -d "legacy" ]; then
    echo -e "${YELLOW}Setting legacy directory permissions...${NC}"
    chmod -R 755 legacy/
    
    # Legacy writable directories
    if [ -d "legacy/uploads" ]; then
        chmod -R 775 legacy/uploads/
    fi
    
    if [ -d "legacy/cache" ]; then
        chmod -R 775 legacy/cache/
    fi
    
    if [ -d "legacy/templates_c" ]; then
        chmod -R 775 legacy/templates_c/
    fi
fi

# Environment file - 600 (only owner can read/write)
if [ -f ".env" ]; then
    echo -e "${YELLOW}Securing .env file...${NC}"
    chmod 600 .env
fi

# Composer files - 644
if [ -f "composer.json" ]; then
    chmod 644 composer.json
fi

if [ -f "composer.lock" ]; then
    chmod 644 composer.lock
fi

# Log files - 644
if [ -d "storage/logs" ]; then
    find storage/logs -name "*.log" -exec chmod 644 {} \;
fi

echo -e "${GREEN}File permissions set successfully!${NC}"

echo -e "${YELLOW}Permission Summary:${NC}"
echo "- Directories: 755 (readable/executable by all)"
echo "- Files: 644 (readable by all, writable by owner)"
echo "- Storage/Cache: 775 (group writable for web server)"
echo "- .env file: 600 (owner only)"
echo "- Legacy writable dirs: 775 (uploads, cache, templates_c)"

echo -e "${YELLOW}Important Notes:${NC}"
echo "1. Ensure your web server user is in the same group as your files"
echo "2. Some shared hosts may require 777 for writable directories"
echo "3. Test your application after setting permissions"
echo "4. Monitor error logs for permission-related issues"

# Check for common permission issues
echo -e "${YELLOW}Checking for potential issues...${NC}"

if [ ! -w "storage" ]; then
    echo -e "${RED}Warning: storage directory is not writable${NC}"
fi

if [ ! -w "bootstrap/cache" ]; then
    echo -e "${RED}Warning: bootstrap/cache directory is not writable${NC}"
fi

if [ -f ".env" ] && [ ! -r ".env" ]; then
    echo -e "${RED}Warning: .env file is not readable${NC}"
fi

echo -e "${GREEN}Permission setup complete!${NC}"
