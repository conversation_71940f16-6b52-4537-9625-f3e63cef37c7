# Shared Hosting Directory Structure

## Recommended Structure

```
/home/<USER>/
├── domains/
│   └── yourdomain.com/
│       ├── public_html/           # Web root (document root)
│       │   ├── public/            # Laravel public directory
│       │   │   ├── index.php      # Laravel entry point
│       │   │   ├── .htaccess      # Backup Apache rules (if needed)
│       │   │   ├── css/
│       │   │   ├── js/
│       │   │   ├── images/
│       │   │   ├── check-legacy-admin.php
│       │   │   ├── check-legacy-module.php
│       │   │   └── legacy/        # Legacy ImpressCMS files
│       │   │       ├── modules/
│       │   │       ├── include/
│       │   │       ├── themes/
│       │   │       └── ...
│       │   └── laravel-app/       # Laravel application (outside web root)
│       │       ├── app/
│       │       ├── bootstrap/
│       │       ├── config/
│       │       ├── database/
│       │       ├── resources/
│       │       ├── routes/
│       │       ├── storage/
│       │       ├── vendor/
│       │       ├── .env
│       │       ├── artisan
│       │       └── composer.json
│       ├── logs/                  # Log files
│       └── tmp/                   # Temporary files
```

## Alternative Structure (if public/ can't be document root)

```
/home/<USER>/
├── domains/
│   └── yourdomain.com/
│       ├── public_html/           # Web root
│       │   ├── index.php          # Modified Laravel entry point
│       │   ├── .htaccess
│       │   ├── css/               # Symlink to ../laravel-app/public/css
│       │   ├── js/                # Symlink to ../laravel-app/public/js
│       │   ├── images/            # Symlink to ../laravel-app/public/images
│       │   ├── check-legacy-admin.php
│       │   ├── check-legacy-module.php
│       │   └── legacy/            # Legacy ImpressCMS files
│       └── laravel-app/           # Laravel application
│           ├── public/            # Original Laravel public directory
│           ├── app/
│           ├── bootstrap/
│           └── ...
```

## Key Points

1. **Security**: Laravel application files (app/, config/, etc.) should be OUTSIDE the web root
2. **Legacy Integration**: Legacy files should be accessible via web but organized
3. **Static Assets**: CSS, JS, images should be in the web root for direct access
4. **Logs**: Keep logs outside web root for security
5. **Permissions**: Proper file permissions are crucial (see section 4)

## Symlinks for Assets (if needed)

If you need to use the alternative structure, create symlinks:

```bash
cd /home/<USER>/domains/yourdomain.com/public_html/
ln -s ../laravel-app/public/css css
ln -s ../laravel-app/public/js js
ln -s ../laravel-app/public/images images
ln -s ../laravel-app/public/fonts fonts
```

## Modified index.php for Alternative Structure

If using alternative structure, modify the index.php in public_html:

```php
<?php
// Modified Laravel entry point for shared hosting
define('LARAVEL_START', microtime(true));

// Adjust paths for shared hosting structure
require __DIR__.'/laravel-app/vendor/autoload.php';
$app = require_once __DIR__.'/laravel-app/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

$response->send();
$kernel->terminate($request, $response);
```
