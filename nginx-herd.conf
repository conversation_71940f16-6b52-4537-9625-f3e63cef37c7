# Lara<PERSON> Herd Nginx Configuration for Dual Routing (Laravel + ImpressCMS Legacy)
# Optimized for Windows development environment with Laravel Herd

server {
    # <PERSON><PERSON> Herd automatically manages ports and SSL
    # No need to specify listen directives
    server_name imlara.test;

    # Windows project path - update to your actual path
    root C:/Users/<USER>/sites/imlara;

    # Default index files
    index index.php index.html index.htm;

    # Let Laravel Herd handle logging
    access_log off;
    error_log off;

    # Security headers optimized for Herd development
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: ws: wss: data: blob: 'unsafe-inline' 'unsafe-eval'" always;

    # Herd development-friendly CORS headers
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;

    # Deny access to sensitive files
    location ~ /\.(?!well-known).* {
        deny all;
    }

    location ~ \.(log|env)$ {
        deny all;
    }

    # Prevent direct access to legacy directory URLs
    location ^~ /legacy/ {
        deny all;
    }

    # Handle static assets - try legacy first, then public
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|pdf|txt|xml|map)$ {
        try_files /legacy$uri /public$uri =404;
        expires 1M;
        add_header Cache-Control "public, immutable";

        # Enable CORS for assets in development
        add_header Access-Control-Allow-Origin "*" always;
    }

    # DUAL ROUTING LOGIC - PHP files
    location ~ \.php$ {
        # Check if the file exists in legacy directory
        set $legacy_file $document_root/legacy$uri;
        set $use_legacy 0;

        # Test if legacy file exists
        if (-f $legacy_file) {
            set $use_legacy 1;
        }

        # Route to legacy PHP file if it exists
        if ($use_legacy = 1) {
            # Laravel Herd PHP-FPM connection (TCP on Windows)
            fastcgi_pass 127.0.0.1:9000;
            fastcgi_index index.php;

            # Critical: Set proper script filename for legacy context
            fastcgi_param SCRIPT_FILENAME $legacy_file;
            fastcgi_param SCRIPT_NAME $uri;
            fastcgi_param REQUEST_URI $request_uri;
            fastcgi_param DOCUMENT_ROOT $document_root/legacy;
            fastcgi_param PHP_SELF $uri;

            # Set working directory to legacy for proper includes
            fastcgi_param PWD $document_root/legacy;

            # Windows/Herd specific environment variables
            fastcgi_param SYSTEMROOT $document_root/legacy;
            fastcgi_param PATH_INFO $fastcgi_path_info;
            fastcgi_param PATH_TRANSLATED $document_root$fastcgi_path_info;
            fastcgi_param SERVER_SOFTWARE "nginx/herd";

            # Herd development settings
            fastcgi_param HERD_ENV "development";
            fastcgi_param CONTEXT "legacy";

            # Timeout settings for development
            fastcgi_read_timeout 300;
            fastcgi_send_timeout 300;
            fastcgi_connect_timeout 60;

            # Buffer settings for better performance
            fastcgi_buffer_size 128k;
            fastcgi_buffers 4 256k;
            fastcgi_busy_buffers_size 256k;

            include fastcgi_params;
            break;
        }

        # If no legacy file, route to Laravel
        try_files /public$uri /public/index.php?$query_string;
    }

    # Main location - handle all other requests
    location / {
        # Try legacy directory first, then public, then Laravel
        try_files /legacy$uri /public$uri /public/index.php?$query_string;
    }

    # Handle PHP files in public directory (Laravel) - Herd optimized
    location ~ ^/public/(.+\.php)$ {
        # Laravel Herd TCP connection
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;

        # Laravel-specific FastCGI parameters
        fastcgi_param SCRIPT_FILENAME $document_root/public/$1;
        fastcgi_param SCRIPT_NAME /$1;
        fastcgi_param DOCUMENT_ROOT $document_root/public;
        fastcgi_param REQUEST_URI $request_uri;
        fastcgi_param QUERY_STRING $query_string;

        # Herd-specific parameters
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_param PATH_TRANSLATED $document_root/public$fastcgi_path_info;
        fastcgi_param SERVER_SOFTWARE "nginx/herd";
        fastcgi_param HERD_ENV "development";
        fastcgi_param CONTEXT "laravel";

        # Development-friendly timeout settings
        fastcgi_read_timeout 300;
        fastcgi_send_timeout 300;
        fastcgi_connect_timeout 60;

        # Buffer settings
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;

        include fastcgi_params;
    }

    # Handle OPTIONS requests for CORS (development)
    location ~ ^/(.*)$ {
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type "text/plain; charset=utf-8";
            add_header Content-Length 0;
            return 204;
        }
    }

    # Remove trailing slashes
    location ~ ^(.+)/$ {
        return 301 $1;
    }

    # Error pages - route through Laravel for better error handling
    error_page 404 /public/index.php;
    error_page 500 502 503 504 /public/index.php;

    # Herd-specific optimizations
    client_max_body_size 100M;
    client_body_timeout 120s;
    client_header_timeout 120s;

    # Enable gzip compression for better performance
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
}
