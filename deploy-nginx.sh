#!/bin/bash

# Nginx Dual Routing Deployment Script for Laravel + ImpressCMS Legacy
# Usage: ./deploy-nginx.sh [project_path] [domain] [php_version]

set -e

# Default values
PROJECT_PATH="${1:-$(pwd)}"
DOMAIN="${2:-imlara.test}"
PHP_VERSION="${3:-8.3}"

echo "🚀 Deploying Nginx Dual Routing Configuration"
echo "=============================================="
echo "Project Path: $PROJECT_PATH"
echo "Domain: $DOMAIN"
echo "PHP Version: $PHP_VERSION"
echo ""

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo "❌ This script should not be run as root. Please run as a regular user with sudo access."
   exit 1
fi

# Check if project directory exists
if [ ! -d "$PROJECT_PATH" ]; then
    echo "❌ Project directory does not exist: $PROJECT_PATH"
    exit 1
fi

# Check if legacy directory exists
if [ ! -d "$PROJECT_PATH/legacy" ]; then
    echo "❌ Legacy directory does not exist: $PROJECT_PATH/legacy"
    echo "Please ensure your ImpressCMS files are in the /legacy subdirectory"
    exit 1
fi

# Check if Laravel is installed
if [ ! -f "$PROJECT_PATH/artisan" ]; then
    echo "❌ Laravel not found. Please ensure Laravel is installed in: $PROJECT_PATH"
    exit 1
fi

echo "✅ Pre-flight checks passed"
echo ""

# Create nginx configuration from template
echo "📝 Creating nginx configuration..."

NGINX_CONFIG="/etc/nginx/sites-available/$DOMAIN"
PHP_SOCKET="/var/run/php/php${PHP_VERSION}-fpm.sock"

# Check if PHP-FPM socket exists
if [ ! -S "$PHP_SOCKET" ]; then
    echo "⚠️  Warning: PHP-FPM socket not found at $PHP_SOCKET"
    echo "   You may need to install PHP-FPM or adjust the PHP version"
    echo "   Available sockets:"
    ls -la /var/run/php/php*-fpm.sock 2>/dev/null || echo "   No PHP-FPM sockets found"
    echo ""
fi

# Create nginx configuration
sudo tee "$NGINX_CONFIG" > /dev/null <<EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    # Document root points to the Laravel project root
    root $PROJECT_PATH;
    
    # Default index files
    index index.php index.html index.htm;
    
    # Logging
    access_log /var/log/nginx/${DOMAIN}.access.log;
    error_log /var/log/nginx/${DOMAIN}.error.log;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Deny access to sensitive files
    location ~ /\.(?!well-known).* {
        deny all;
    }
    
    location ~ \.(log|env)$ {
        deny all;
    }
    
    # Prevent direct access to legacy directory
    location ^~ /legacy/ {
        deny all;
    }
    
    # Handle static assets - try legacy first, then public
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|pdf|txt|xml)$ {
        try_files /legacy\$uri /public\$uri =404;
        expires 1M;
        add_header Cache-Control "public, immutable";
    }
    
    # Special handling for PHP files that exist in legacy directory
    location ~ \.php$ {
        # Check if the file exists in legacy directory
        set \$legacy_file \$document_root/legacy\$uri;
        
        # If legacy file exists, process it with proper context
        if (-f \$legacy_file) {
            # Set up FastCGI parameters for legacy context
            fastcgi_pass unix:$PHP_SOCKET;
            fastcgi_index index.php;
            
            # Critical: Set the script filename to the legacy file
            fastcgi_param SCRIPT_FILENAME \$legacy_file;
            fastcgi_param SCRIPT_NAME \$uri;
            fastcgi_param REQUEST_URI \$request_uri;
            fastcgi_param DOCUMENT_ROOT \$document_root/legacy;
            fastcgi_param PHP_SELF \$uri;
            
            # Set working directory to legacy for proper includes
            fastcgi_param PWD \$document_root/legacy;
            
            # Include standard fastcgi params
            include fastcgi_params;
            
            # Override some critical params for legacy context
            fastcgi_param ORIG_SCRIPT_FILENAME \$legacy_file;
            fastcgi_param CONTEXT "legacy";
            
            break;
        }
        
        # If no legacy file, route to Laravel
        try_files /public\$uri /public/index.php?\$query_string;
    }
    
    # Main location - handle all other requests
    location / {
        # Try legacy directory first, then public, then Laravel
        try_files /legacy\$uri /public\$uri /public/index.php?\$query_string;
    }
    
    # Handle PHP files in public directory (Laravel)
    location ~ ^/public/(.+\.php)$ {
        fastcgi_pass unix:$PHP_SOCKET;
        fastcgi_index index.php;
        
        fastcgi_param SCRIPT_FILENAME \$document_root/public/\$1;
        fastcgi_param SCRIPT_NAME /\$1;
        fastcgi_param DOCUMENT_ROOT \$document_root/public;
        fastcgi_param REQUEST_URI \$request_uri;
        fastcgi_param QUERY_STRING \$query_string;
        
        include fastcgi_params;
    }
    
    # Remove trailing slashes
    location ~ ^(.+)/$ {
        return 301 \$1;
    }
    
    # Error pages
    error_page 404 /public/index.php;
    error_page 500 502 503 504 /public/index.php;
}
EOF

echo "✅ Nginx configuration created: $NGINX_CONFIG"

# Enable the site
echo "🔗 Enabling nginx site..."
sudo ln -sf "/etc/nginx/sites-available/$DOMAIN" "/etc/nginx/sites-enabled/$DOMAIN"

# Add to hosts file if not already present
if ! grep -q "$DOMAIN" /etc/hosts; then
    echo "📝 Adding $DOMAIN to /etc/hosts..."
    echo "127.0.0.1 $DOMAIN" | sudo tee -a /etc/hosts
else
    echo "✅ $DOMAIN already in /etc/hosts"
fi

# Test nginx configuration
echo "🧪 Testing nginx configuration..."
if sudo nginx -t; then
    echo "✅ Nginx configuration is valid"
else
    echo "❌ Nginx configuration test failed"
    exit 1
fi

# Reload nginx
echo "🔄 Reloading nginx..."
sudo systemctl reload nginx

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "Next steps:"
echo "==========="
echo "1. Visit http://$DOMAIN in your browser"
echo "2. You should see the ImpressCMS installation wizard"
echo "3. Test Laravel routes: http://$DOMAIN/laravel-test"
echo "4. Run the test script: php test-nginx-routing.php"
echo ""
echo "Troubleshooting:"
echo "================"
echo "- Check nginx logs: sudo tail -f /var/log/nginx/${DOMAIN}.error.log"
echo "- Check PHP-FPM logs: sudo tail -f /var/log/php${PHP_VERSION}-fpm.log"
echo "- Test configuration: sudo nginx -t"
echo ""
echo "If you encounter issues, refer to NGINX_SETUP_GUIDE.md for detailed troubleshooting."
