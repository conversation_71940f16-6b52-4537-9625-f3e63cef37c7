server {
    listen 80;
    server_name imlara.test;

    # Document root points to the Laravel project root
    root /path/to/your/imlara/project;

    # Default index files
    index index.php index.html index.htm;

    # Logging
    access_log /var/log/nginx/imlara.test.access.log;
    error_log /var/log/nginx/imlara.test.error.log;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Deny access to sensitive files
    location ~ /\.(?!well-known).* {
        deny all;
    }

    location ~ \.(log|env)$ {
        deny all;
    }

    # Handle static assets - try legacy first, then public
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|pdf|txt|xml)$ {
        try_files /legacy$uri /public$uri =404;
        expires 1M;
        add_header Cache-Control "public, immutable";
    }

    # Main location - route everything through Laravel with dual routing
    location / {
        # Try files in this order:
        # 1. Legacy directory (for direct file access)
        # 2. Public directory (for Laravel assets)
        # 3. Laravel front controller (which will handle dual routing via middleware)
        try_files /legacy$uri /public$uri /public/index.php?$query_string;
    }

    # Handle PHP files in legacy directory
    location ~ ^/legacy/(.+\.php)$ {
        internal;  # Only allow internal redirects to this location

        # Set the actual file path
        set $legacy_script /legacy/$1;

        # Ensure the file exists
        try_files $legacy_script =404;

        # Process with PHP-FPM
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;  # Adjust PHP version as needed
        fastcgi_index index.php;

        # Critical: Set proper script filename and working directory for legacy context
        fastcgi_param SCRIPT_FILENAME $document_root$legacy_script;
        fastcgi_param SCRIPT_NAME /$1;  # Remove /legacy prefix from script name
        fastcgi_param REQUEST_URI $request_uri;
        fastcgi_param DOCUMENT_ROOT $document_root/legacy;
        fastcgi_param PHP_SELF /$1;

        # Set working directory to legacy folder
        fastcgi_param PWD $document_root/legacy;

        include fastcgi_params;
    }

    # Handle PHP files in public directory (Laravel)
    location ~ ^/public/(.+\.php)$ {
        internal;  # Only allow internal redirects to this location

        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;  # Adjust PHP version as needed
        fastcgi_index index.php;

        fastcgi_param SCRIPT_FILENAME $document_root/public/$1;
        fastcgi_param SCRIPT_NAME /$1;
        fastcgi_param DOCUMENT_ROOT $document_root/public;

        include fastcgi_params;
    }

    # Remove trailing slashes
    location ~ ^(.+)/$ {
        return 301 $1;
    }
}
