<?php
/**
 * Test script to validate dual routing system
 * This script tests both the router.php and Laravel middleware approaches
 */

echo "=== Dual Routing System Test ===\n\n";

// Test 1: Check if legacy files exist
echo "1. Testing legacy file detection:\n";
$testFiles = [
    '/test-legacy.php',
    '/admin.php', 
    '/index.php',
    '/install/index.php',
    '/install/',
    '/nonexistent.php'
];

foreach ($testFiles as $file) {
    $fullPath = __DIR__ . '/legacy' . $file;
    $exists = file_exists($fullPath);
    $isFile = is_file($fullPath);
    $isDir = is_dir($fullPath);
    
    echo "  $file: ";
    if ($isDir) {
        $indexPath = rtrim($fullPath, '/') . '/index.php';
        $hasIndex = file_exists($indexPath);
        echo "DIR" . ($hasIndex ? " (has index.php)" : " (no index.php)");
    } elseif ($isFile) {
        echo "FILE";
    } else {
        echo "NOT FOUND";
    }
    echo "\n";
}

echo "\n2. Testing router.php logic:\n";

// Simulate router.php logic
function testRouterLogic($requestUri) {
    $requestUri = ltrim($requestUri, '/');
    $legacyPath = __DIR__ . '/legacy/' . $requestUri;
    
    if (file_exists($legacyPath) && is_file($legacyPath)) {
        return "LEGACY FILE: $legacyPath";
    } elseif (is_dir($legacyPath)) {
        $indexPath = rtrim($legacyPath, '/') . '/index.php';
        if (file_exists($indexPath)) {
            return "LEGACY DIR INDEX: $indexPath";
        }
    }
    
    if (file_exists(__DIR__ . '/public/index.php')) {
        return "LARAVEL FALLBACK";
    }
    
    return "404 NOT FOUND";
}

foreach ($testFiles as $file) {
    $result = testRouterLogic($file);
    echo "  $file -> $result\n";
}

echo "\n3. Testing Laravel middleware logic:\n";

// Simulate middleware logic
function testMiddlewareLogic($path) {
    // Skip paths
    $skipPaths = ['/api/', '/laravel-test', '/_debugbar/', '/telescope/', '/horizon/', '/up'];
    
    foreach ($skipPaths as $skipPath) {
        if (str_starts_with($path, $skipPath)) {
            return "SKIPPED (Laravel path)";
        }
    }
    
    $legacyFilePath = __DIR__ . '/legacy' . $path;
    
    // Handle directory requests - look for index.php
    if (is_dir($legacyFilePath)) {
        $indexPath = rtrim($legacyFilePath, '/') . '/index.php';
        if (file_exists($indexPath) && is_readable($indexPath)) {
            return "LEGACY DIR: $indexPath";
        }
    }
    
    if (file_exists($legacyFilePath) && is_file($legacyFilePath) && is_readable($legacyFilePath)) {
        return "LEGACY FILE: $legacyFilePath";
    }
    
    return "CONTINUE TO LARAVEL";
}

$middlewareTests = array_merge($testFiles, ['/laravel-test', '/api/test', '/up']);

foreach ($middlewareTests as $path) {
    $result = testMiddlewareLogic($path);
    echo "  $path -> $result\n";
}

echo "\n4. Path normalization test:\n";
$pathTests = [
    '/install/',
    '/install',
    'install/',
    'install',
    '/test-legacy.php',
    'test-legacy.php'
];

foreach ($pathTests as $path) {
    $normalized = '/' . ltrim($path, '/');
    $legacyPath = __DIR__ . '/legacy' . $normalized;
    echo "  '$path' -> '$normalized' -> " . (file_exists($legacyPath) ? "EXISTS" : "NOT FOUND") . "\n";
}

echo "\n=== Test Complete ===\n";
?>
