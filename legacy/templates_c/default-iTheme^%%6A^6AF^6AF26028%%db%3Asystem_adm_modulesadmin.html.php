<?php /* Smarty version 2.6.31, created on 2025-07-31 23:18:15
         compiled from db:system_adm_modulesadmin.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'cycle', 'db:system_adm_modulesadmin.html', 15, false),)), $this); ?>
<div class="CPbigTitle" style="background-image: url(<?php echo $this->_tpl_vars['xoops_url']; ?>
/modules/system/admin/modulesadmin/images/modulesadmin_big.png)"><?php echo $this->_tpl_vars['lang_madmin']; ?>
</div><br />
<h2><?php echo $this->_tpl_vars['lang_installed']; ?>
</h2>
<form action='admin.php' method='post' name='moduleadmin' id='moduleadmin'>
<table width="100%" cellpadding="4" cellspacing="1" border="0" class="outer">
  <tr align='center' valign='middle'>
    <th width="40%"><?php echo $this->_tpl_vars['lang_module']; ?>
</th>
    <th><?php echo $this->_tpl_vars['lang_version']; ?>
</th>
    <th><?php echo $this->_tpl_vars['lang_modstatus']; ?>
</th>
    <th><?php echo $this->_tpl_vars['lang_lastup']; ?>
</th>
    <th><?php echo $this->_tpl_vars['lang_active']; ?>
</th>
    <th><?php echo $this->_tpl_vars['lang_order']; ?>
<br /><small><?php echo $this->_tpl_vars['lang_order0']; ?>
</small></th>
    <th width='130px'><?php echo $this->_tpl_vars['lang_action']; ?>
</th>
  </tr>
  <?php $_from = $this->_tpl_vars['modules']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['module']):
?>
    <tr valign='middle'class="<?php echo smarty_function_cycle(array('values' => "even,odd"), $this);?>
">
      <td align="<?php echo @_GLOBAL_LEFT; ?>
" valign="middle">
        <div id="modlogo" style="float: <?php echo @_GLOBAL_LEFT; ?>
; padding: 2px;">
          <?php if ($this->_tpl_vars['module']['hasadmin'] == 1 && $this->_tpl_vars['module']['isactive'] == '1'): ?>
            <a href="<?php echo $this->_tpl_vars['xoops_url']; ?>
/modules/<?php echo $this->_tpl_vars['module']['dirname']; ?>
/<?php echo $this->_tpl_vars['module']['adminindex']; ?>
">
              <img src="<?php echo $this->_tpl_vars['xoops_url']; ?>
/modules/<?php echo $this->_tpl_vars['module']['dirname']; ?>
/<?php echo $this->_tpl_vars['module']['image']; ?>
" alt="<?php echo $this->_tpl_vars['module']['name']; ?>
" title="<?php echo $this->_tpl_vars['module']['name']; ?>
" border="0" />
            </a>
          <?php else: ?>
            <img src="<?php echo $this->_tpl_vars['xoops_url']; ?>
/modules/<?php echo $this->_tpl_vars['module']['dirname']; ?>
/<?php echo $this->_tpl_vars['module']['image']; ?>
" alt="<?php echo $this->_tpl_vars['module']['name']; ?>
" title="<?php echo $this->_tpl_vars['module']['name']; ?>
" border="0" />
          <?php endif; ?>&nbsp;
        </div>
        <div id="modlogo" style="float: <?php echo @_GLOBAL_LEFT; ?>
; padding-top: 2px;">
          <b><?php echo $this->_tpl_vars['lang_modulename']; ?>
: </b><?php echo $this->_tpl_vars['module']['name']; ?>
<br />
          <b><?php echo $this->_tpl_vars['lang_moduletitle']; ?>
: </b><input type="text" name="newname[<?php echo $this->_tpl_vars['module']['mid']; ?>
]" value="<?php echo $this->_tpl_vars['module']['title']; ?>
" maxlength="150" size="30" />
        </div>
        <input type="hidden" name="oldname[<?php echo $this->_tpl_vars['module']['mid']; ?>
]" value="<?php echo $this->_tpl_vars['module']['title']; ?>
" />
      </td>
      <td align='center' valign="middle"><?php echo $this->_tpl_vars['module']['version']; ?>
</td>
      <td align='center' valign="middle"><?php echo $this->_tpl_vars['module']['status']; ?>
</td>
      <td align='center' valign="middle"><?php echo $this->_tpl_vars['module']['last_update']; ?>
</td>
      <td align='center' valign="middle">
        <?php if ($this->_tpl_vars['module']['dirname'] == 'system'): ?>
          <input type="hidden" name="newstatus[<?php echo $this->_tpl_vars['module']['mid']; ?>
]" value="1" />
          <input type="hidden" name="oldstatus[<?php echo $this->_tpl_vars['module']['mid']; ?>
]" value="1" />
        <?php else: ?>
          <?php if ($this->_tpl_vars['module']['isactive'] == '1'): ?>
            <input type="checkbox" name="newstatus[<?php echo $this->_tpl_vars['module']['mid']; ?>
]" value="1" checked="checked" />
            <input type="hidden" name="oldstatus[<?php echo $this->_tpl_vars['module']['mid']; ?>
]" value="1" />
          <?php else: ?>
            <input type="checkbox" name="newstatus[<?php echo $this->_tpl_vars['module']['mid']; ?>
]" value="1" />
            <input type="hidden" name="oldstatus[<?php echo $this->_tpl_vars['module']['mid']; ?>
]" value="0" />
          <?php endif; ?>
        <?php endif; ?>
      </td>
      <td align='center' valign="middle">
        <?php if ($this->_tpl_vars['module']['hasmain'] == '1'): ?>
          <input type="hidden" name="oldweight[<?php echo $this->_tpl_vars['module']['mid']; ?>
]" value="<?php echo $this->_tpl_vars['module']['weight']; ?>
" />
          <input type="text" name="weight[<?php echo $this->_tpl_vars['module']['mid']; ?>
]" size="3" maxlength="5" value="<?php echo $this->_tpl_vars['module']['weight']; ?>
" />
        <?php else: ?>
          <input type="hidden" name="oldweight[<?php echo $this->_tpl_vars['module']['mid']; ?>
]" value="0" />
          <input type="hidden" name="weight[<?php echo $this->_tpl_vars['module']['mid']; ?>
]" value="0" />
        <?php endif; ?>
      </td>
      <td align='center' valign="middle">
        <?php if ($this->_tpl_vars['module']['support_site_url'] != '' && $this->_tpl_vars['module']['isactive'] == '1'): ?>
          <a href="<?php echo $this->_tpl_vars['module']['support_site_url']; ?>
" rel="external"><img src="<?php echo $this->_tpl_vars['xoops_url']; ?>
/modules/system/images/support.png" alt="<?php echo $this->_tpl_vars['lang_support']; ?>
" title="<?php echo $this->_tpl_vars['lang_support']; ?>
"/></a>
        <?php endif; ?>
        <a href="<?php echo $this->_tpl_vars['xoops_url']; ?>
/modules/system/admin.php?fct=modulesadmin&amp;op=update&amp;module=<?php echo $this->_tpl_vars['module']['dirname']; ?>
"><img src="<?php echo $this->_tpl_vars['xoops_url']; ?>
/modules/system/images/update.png" alt="<?php echo $this->_tpl_vars['lang_update']; ?>
" title="<?php echo $this->_tpl_vars['lang_update']; ?>
"/></a>
        <?php if ($this->_tpl_vars['module']['isactive'] != '1'): ?>
          <a href="<?php echo $this->_tpl_vars['xoops_url']; ?>
/modules/system/admin.php?fct=modulesadmin&amp;op=uninstall&amp;module=<?php echo $this->_tpl_vars['module']['dirname']; ?>
"><img src="<?php echo $this->_tpl_vars['xoops_url']; ?>
/modules/system/images/uninstall.png" alt="<?php echo $this->_tpl_vars['lang_unistall']; ?>
" title="<?php echo $this->_tpl_vars['lang_unistall']; ?>
" /></a>
        <?php endif; ?>  
        <a href='javascript:openWithSelfMain("<?php echo $this->_tpl_vars['xoops_url']; ?>
/modules/system/admin.php?fct=version&amp;mid=<?php echo $this->_tpl_vars['module']['mid']; ?>
","Info",300,230);'><img src="<?php echo $this->_tpl_vars['xoops_url']; ?>
/modules/system/images/info.png" alt="<?php echo $this->_tpl_vars['lang_info']; ?>
" title="<?php echo $this->_tpl_vars['lang_info']; ?>
" /></a>
        <input type="hidden" name="module[]" value="<?php echo $this->_tpl_vars['module']['mid']; ?>
" />
      </td>
    </tr>
  <?php endforeach; endif; unset($_from); ?>
  <tr class='foot'>
    <td colspan='7' align='center'>
      <input type='hidden' name='fct' value='modulesadmin' />
      <input type='hidden' name='op' value='confirm' />
      <input type='submit' name='submit' value='<?php echo $this->_tpl_vars['lang_submit']; ?>
' />
    </td>
  </tr>
</table>
</form>
<br />
<h2><?php echo $this->_tpl_vars['lang_noninstall']; ?>
</h2>
<table width='100%' border='0' class='outer' cellpadding='4' cellspacing='1'>
  <tr align='center'>
    <th><?php echo $this->_tpl_vars['lang_module']; ?>
</th>
    <th><?php echo $this->_tpl_vars['lang_version']; ?>
</th>
    <th><?php echo $this->_tpl_vars['lang_modstatus']; ?>
</th>
    <th width='130px'><?php echo $this->_tpl_vars['lang_action']; ?>
</th>
  </tr>
  <?php $_from = $this->_tpl_vars['avmodules']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['module']):
?>
    <tr valign='middle'class="<?php echo smarty_function_cycle(array('values' => "even,odd"), $this);?>
">
      <td>
        <div id="modlogo" style="padding: 2px;"><img src="<?php echo $this->_tpl_vars['xoops_url']; ?>
/modules/<?php echo $this->_tpl_vars['module']['dirname']; ?>
/<?php echo $this->_tpl_vars['module']['image']; ?>
" alt="<?php echo $this->_tpl_vars['module']['name']; ?>
" alt="<?php echo $this->_tpl_vars['module']['name']; ?>
" border="0" />&nbsp;</div>
	    <div id="modlogo" style="padding-top: 10px;"> <b><?php echo $this->_tpl_vars['lang_modulename']; ?>
: </b><?php echo $this->_tpl_vars['module']['name']; ?>
<br /> </div>
      </td>
      <td align='center'><?php echo $this->_tpl_vars['module']['version']; ?>
</td>
      <td align='center'><?php echo $this->_tpl_vars['module']['status']; ?>
</td>
      <td width='130px' align='center'>
        <a href="<?php echo $this->_tpl_vars['xoops_url']; ?>
/modules/system/admin.php?fct=modulesadmin&op=install&module=<?php echo $this->_tpl_vars['module']['dirname']; ?>
"><img src="<?php echo $this->_tpl_vars['xoops_url']; ?>
/modules/system/images/install.png" alt="<?php echo $this->_tpl_vars['lang_install']; ?>
" title="<?php echo $this->_tpl_vars['lang_install']; ?>
" /></a>
        <a href='javascript:openWithSelfMain("<?php echo $this->_tpl_vars['xoops_url']; ?>
/modules/system/admin.php?fct=version&mid=<?php echo $this->_tpl_vars['module']['dirname']; ?>
","Info",300,230);'><img src="<?php echo $this->_tpl_vars['xoops_url']; ?>
/modules/system/images/info.png" alt="<?php echo $this->_tpl_vars['lang_info']; ?>
" title="<?php echo $this->_tpl_vars['lang_info']; ?>
" /></a>
      </td>
    </tr>
  <?php endforeach; endif; unset($_from); ?>
</table>