<?php /* Smarty version 2.6.31, created on 2025-07-31 23:18:07
         compiled from C:/Users/<USER>/PhpstormProjects/imlara/legacy/themes/iTheme/theme_admin.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'substr', 'C:/Users/<USER>/PhpstormProjects/imlara/legacy/themes/iTheme/theme_admin.html', 182, false),)), $this); ?>
<?php 
	if( ! empty( $_SESSION['redirect_message'] ) ) {
		$this->_tpl_vars['site_msg'] = $_SESSION['redirect_message'];
		unset( $_SESSION['redirect_message'] ) ;
	}
 ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="<?php echo $this->_tpl_vars['icms_langcode']; ?>
">
<head>
<!-- Center block custom positioning -->
		<?php $this->assign('theme_top_order', 'lrc'); ?>
	<?php $this->assign('theme_bottom_order', 'lcr'); ?>

<!-- Theme name -->
	<?php $this->assign('theme_name', $this->_tpl_vars['xoTheme']->folderName); ?>

<!-- Title and meta -->
	<title><?php echo @_IMPRESSCMS_ADMIN; ?>
 <?php echo $this->_tpl_vars['icms_sitename']; ?>
</title>
	<meta http-equiv="content-type" content="text/html; charset=<?php echo $this->_tpl_vars['icms_charset']; ?>
" />
	<meta name="robots" content="<?php echo $this->_tpl_vars['icms_meta_robots']; ?>
" />
	<meta name="keywords" content="<?php echo $this->_tpl_vars['icms_meta_keywords']; ?>
" />
	<meta name="description" content="<?php echo $this->_tpl_vars['icms_meta_description']; ?>
" />
	<meta name="rating" content="<?php echo $this->_tpl_vars['icms_meta_rating']; ?>
" />
	<meta name="author" content="<?php echo $this->_tpl_vars['icms_meta_author']; ?>
" />
	<meta name="copyright" content="<?php echo $this->_tpl_vars['icms_meta_copyright']; ?>
" />
	<meta name="generator" content="ImpressCMS" />

<!-- Favicon -->
	<link rel="shortcut icon" type="image/ico" href="<?php 
echo 'http://imlarastorm.test/themes/iTheme/icons/favicon.ico'; ?>" />
	<link rel="icon" type="image/png" href="<?php 
echo 'http://imlarastorm.test/themes/iTheme/icons/icon.png'; ?>" />

<!-- Module Header -->
		<?php echo $this->_tpl_vars['icms_module_header']; ?>


<!-- Sheet Css -->
	<link rel="stylesheet" type="text/css" media="all" href="<?php echo $this->_tpl_vars['icms_url']; ?>
/modules/system/style<?php if ($this->_tpl_vars['icms_rtl']): ?>_rtl<?php endif; ?>.css" />
	<link rel="stylesheet" type="text/css" media="all" title="Style sheet" href="<?php echo $this->_tpl_vars['icms_imageurl']; ?>
<?php if ($this->_tpl_vars['icms_rtl']): ?>rtl/<?php endif; ?>style_admin.css" />

</head>

<body id="<?php echo $this->_tpl_vars['icms_dirname']; ?>
" class="<?php echo $this->_tpl_vars['icms_langcode']; ?>
">
<?php if ($this->_tpl_vars['xoBlocks']['canvas_left_admin'] && $this->_tpl_vars['xoBlocks']['canvas_right_admin']): ?>
<?php $this->assign('columns_layout', 'threecolumns-layout'); ?>
<?php elseif ($this->_tpl_vars['xoBlocks']['canvas_left_admin']): ?>
<?php $this->assign('columns_layout', 'leftcolumn-layout'); ?>
<?php elseif ($this->_tpl_vars['xoBlocks']['canvas_right_admin']): ?>
<?php $this->assign('columns_layout', 'rightcolumn-layout'); ?>
<?php endif; ?>
<div id="xo-canvas"<?php if ($this->_tpl_vars['columns_layout']): ?> class="<?php echo $this->_tpl_vars['columns_layout']; ?>
"<?php endif; ?>>
	<!-- Header -->

<div id="logoHead">
	<span class="<?php echo @_GLOBAL_LEFT; ?>
">
		<?php if ($this->_tpl_vars['adm_left_logo'] == ''): ?>&nbsp;<?php else: ?>
		    <?php if ($this->_tpl_vars['adm_left_logo_url'] != ''): ?>
			    <a href="<?php echo $this->_tpl_vars['adm_left_logo_url']; ?>
" title="<?php if ($this->_tpl_vars['adm_left_logo_alt'] != ''): ?><?php echo $this->_tpl_vars['adm_left_logo_alt']; ?>
<?php else: ?>ImpressCMS<?php endif; ?>">
			<?php endif; ?>
			<img src="<?php echo $this->_tpl_vars['icms_url']; ?>
<?php echo $this->_tpl_vars['adm_left_logo']; ?>
" alt="<?php if ($this->_tpl_vars['adm_left_logo_alt'] != ''): ?><?php echo $this->_tpl_vars['adm_left_logo_alt']; ?>
<?php else: ?>ImpressCMS<?php endif; ?>"/>
		    <?php if ($this->_tpl_vars['adm_left_logo_url'] != ''): ?>
			    </a>
			<?php endif; ?>
		<?php endif; ?>
	</span>
	<span class="<?php echo @_GLOBAL_RIGHT; ?>
">
		<?php if ($this->_tpl_vars['adm_right_logo'] == ''): ?>&nbsp;<?php else: ?>
		    <?php if ($this->_tpl_vars['adm_right_logo_url'] != ''): ?>
			    <a href="<?php echo $this->_tpl_vars['adm_right_logo_url']; ?>
" title="<?php if ($this->_tpl_vars['adm_right_logo_alt'] != ''): ?><?php echo $this->_tpl_vars['adm_right_logo_alt']; ?>
<?php else: ?>ImpressCMS<?php endif; ?>">
			<?php endif; ?>
			<img src="<?php echo $this->_tpl_vars['icms_url']; ?>
<?php echo $this->_tpl_vars['adm_right_logo']; ?>
" alt="<?php if ($this->_tpl_vars['adm_right_logo_alt'] != ''): ?><?php echo $this->_tpl_vars['adm_right_logo_alt']; ?>
<?php else: ?>ImpressCMS<?php endif; ?>"/>
		    <?php if ($this->_tpl_vars['adm_right_logo_url'] != ''): ?>
			    </a>
			<?php endif; ?>
		<?php endif; ?>
	</span>
	<div class="clear"></div>
</div>
<div id="navbarCP">
  <ul id="nav">
    <?php $_from = $this->_tpl_vars['navitems']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['item']):
?>
      <?php if (( $this->_tpl_vars['systemadm'] || $this->_tpl_vars['item']['id'] != 'opsystem' ) && ( $this->_tpl_vars['item']['id'] != 'news' || $this->_tpl_vars['show_impresscms_menu'] )): ?>
        <?php if ($this->_tpl_vars['modulesadm'] || $this->_tpl_vars['item']['id'] != 'modules'): ?>
	      <li><a href="<?php echo $this->_tpl_vars['item']['link']; ?>
"><img src="<?php echo $this->_tpl_vars['icms_url']; ?>
/modules/system/images/arrow1.gif" alt="arrow"/>&nbsp;<?php echo $this->_tpl_vars['item']['text']; ?>
</a>
	        <ul>
		      <?php $_from = $this->_tpl_vars['item']['menu']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['sub']):
?>
                <?php if ($this->_tpl_vars['item']['id'] == 'cphome'): ?>
		          <li><a href="<?php if ($this->_tpl_vars['sub']['absolute'] == 1): ?><?php echo $this->_tpl_vars['sub']['link']; ?>
<?php else: ?><?php echo $this->_tpl_vars['icms_url']; ?>
/modules/<?php echo $this->_tpl_vars['sub']['dir']; ?>
/<?php echo $this->_tpl_vars['sub']['link']; ?>
<?php endif; ?>" style="background-image: url(<?php if ($this->_tpl_vars['sub']['small'] != ''): ?><?php echo $this->_tpl_vars['sub']['small']; ?>
<?php else: ?><?php echo $this->_tpl_vars['icms_url']; ?>
/modules/system/images/imagesarrow1.gif<?php endif; ?>);"><?php echo $this->_tpl_vars['sub']['title']; ?>
</a></li>
                <?php elseif ($this->_tpl_vars['item']['id'] == 'opsystem'): ?>
                  <li><a href="<?php if ($this->_tpl_vars['sub']['absolute'] == 1): ?><?php echo $this->_tpl_vars['sub']['link']; ?>
<?php else: ?><?php echo $this->_tpl_vars['icms_url']; ?>
/modules/<?php echo $this->_tpl_vars['sub']['dir']; ?>
/<?php echo $this->_tpl_vars['sub']['link']; ?>
<?php endif; ?>"><?php echo $this->_tpl_vars['sub']['title']; ?>
</a>
                    <?php if ($this->_tpl_vars['sub']['hassubs']): ?>
                      <ul>
                        <?php $_from = $this->_tpl_vars['sub']['subs']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['subitem']):
?>
                          <li><a href="<?php echo $this->_tpl_vars['subitem']['link']; ?>
" style="background-image: url(<?php if ($this->_tpl_vars['subitem']['small'] != ''): ?><?php echo $this->_tpl_vars['icms_url']; ?>
/modules/system/<?php echo $this->_tpl_vars['subitem']['small']; ?>
<?php else: ?><?php echo $this->_tpl_vars['icms_url']; ?>
/modules/system/images/imagesarrow1.gif<?php endif; ?>);"><?php echo $this->_tpl_vars['subitem']['title']; ?>
</a>
                            <?php if ($this->_tpl_vars['subitem']['hassubs']): ?>
                              <ul>
                                <?php $_from = $this->_tpl_vars['subitem']['subs']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['subsubitem']):
?>
                                  <li><a href="<?php echo $this->_tpl_vars['subsubitem']['link']; ?>
"><?php echo $this->_tpl_vars['subsubitem']['title']; ?>
</a></li>
                                <?php endforeach; endif; unset($_from); ?>
                              </ul>
                            <?php endif; ?>
                          </li>
                        <?php endforeach; endif; unset($_from); ?>
                      </ul>
                    <?php endif; ?>
                  </li>
                <?php elseif ($this->_tpl_vars['item']['id'] == 'modules'): ?>
		          <li><a href="<?php if ($this->_tpl_vars['sub']['absolute'] == 1): ?><?php echo $this->_tpl_vars['sub']['link']; ?>
<?php else: ?><?php echo $this->_tpl_vars['icms_url']; ?>
/modules/<?php echo $this->_tpl_vars['sub']['dir']; ?>
/<?php echo $this->_tpl_vars['sub']['link']; ?>
<?php endif; ?>" style="background-image: url(<?php if ($this->_tpl_vars['sub']['small'] != ''): ?><?php echo $this->_tpl_vars['sub']['small']; ?>
<?php else: ?><?php echo $this->_tpl_vars['icms_url']; ?>
/modules/system/images/modulesitem.png<?php endif; ?>);"><?php echo $this->_tpl_vars['sub']['title']; ?>
</a>
    		      <?php if ($this->_tpl_vars['sub']['hassubs']): ?>
	    	        <ul>
		              <?php $_from = $this->_tpl_vars['sub']['subs']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['subitem']):
?>
		                <li><a href="<?php echo $this->_tpl_vars['subitem']['link']; ?>
"><?php echo $this->_tpl_vars['subitem']['title']; ?>
</a></li>
		              <?php endforeach; endif; unset($_from); ?>
		            </ul>
		          <?php endif; ?>
		          </li>
                <?php elseif ($this->_tpl_vars['item']['id'] == 'news' && $this->_tpl_vars['show_impresscms_menu']): ?>
    		      <li><a rel="external" href="<?php if ($this->_tpl_vars['sub']['absolute'] == 1): ?><?php echo $this->_tpl_vars['sub']['link']; ?>
<?php else: ?><?php echo $this->_tpl_vars['icms_url']; ?>
/modules/<?php echo $this->_tpl_vars['sub']['dir']; ?>
/<?php echo $this->_tpl_vars['sub']['link']; ?>
<?php endif; ?>" style="background-image: url(<?php if ($this->_tpl_vars['sub']['small'] != ''): ?><?php echo $this->_tpl_vars['sub']['small']; ?>
<?php endif; ?>);"><?php echo $this->_tpl_vars['sub']['title']; ?>
</a></li>
                <?php endif; ?>
		      <?php endforeach; endif; unset($_from); ?>
		    </ul>
	      </li>
	    <?php endif; ?>
	  <?php endif; ?>
    <?php endforeach; endif; unset($_from); ?>
  </ul>
  <?php if ($this->_tpl_vars['ml_is_enabled']): ?>
	<span id="nav-change-language">[mlimg]</span>
  <?php endif; ?>
</div>
<?php if ($this->_tpl_vars['modname'] != ''): ?>
  <div id="navOptionsCP">
	<div class="modname">
	  <?php echo $this->_tpl_vars['modname']; ?>

	</div>
	<?php $_from = $this->_tpl_vars['mod_options']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['op']):
?>
	  <a href="<?php echo $this->_tpl_vars['op']['link']; ?>
" title="<?php echo $this->_tpl_vars['op']['title']; ?>
">
        <?php if ($this->_tpl_vars['op']['title'] == $this->_tpl_vars['lang_prefs'] && $this->_tpl_vars['moddir'] != 'system'): ?>
          <img src="<?php echo $this->_tpl_vars['icms_url']; ?>
/modules/system/images/prefs.png" alt="<?php echo $this->_tpl_vars['lang_preferences']; ?>
" />
        <?php else: ?>
          <img src="<?php if ($this->_tpl_vars['op']['icon'] == ''): ?><?php echo $this->_tpl_vars['icms_url']; ?>
/images/icon_options.png<?php else: ?><?php echo $this->_tpl_vars['modpath']; ?>
/<?php echo $this->_tpl_vars['op']['icon']; ?>
<?php endif; ?>" alt="<?php echo $this->_tpl_vars['op']['title']; ?>
" />
        <?php endif; ?>
      </a>
	<?php endforeach; endif; unset($_from); ?>
  </div>
<?php endif; ?>

<!-- Help Bar -->
<!--<?php if ($this->_tpl_vars['icms_dirname'] == 'system'): ?>-->
<!--<?php if ($_GET['op'] != 'showmod'): ?>-->
<!--<?php if ($_GET['fct'] != ''): ?>-->
<!--<div id="helpbar"><a href="http://wiki.impresscms.org/index.php?title=Admin_<?php echo $_GET['fct']; ?>
">?</a></div>-->
<!--<?php else: ?>-->
<!--<div id="helpbar"><a href="http://wiki.impresscms.org/index.php?title=Category:System">?</a></div>-->
<!--<?php endif; ?>-->
<!--<?php endif; ?>-->
<!--<?php endif; ?>-->
<!-- to be enabled at later date -->
	<!-- Content -->
	<div style="width: 100%; height: 11px; background: transparent; clear: both;">&nbsp;</div>
	<div style="width: 100%; height: 10px; background: transparent; clear: both;">&nbsp;</div>
	<div id="xo-canvas-content">
		<div id="xo-canvas-columns">
			<!-- Left column -->
				<?php if ($this->_tpl_vars['icms_rtl']): ?>
			<?php if ($this->_tpl_vars['xoBlocks']['canvas_right_admin']): ?>
			<?php $this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme_name'])."/blockszone.html", 'smarty_include_vars' => array('blocks' => $this->_tpl_vars['xoBlocks']['canvas_right_admin'],'zoneClass' => 'xo-canvas-column','zoneId' => 'xo-canvas-rightcolumn')));
 ?>
			<?php endif; ?>
			<?php else: ?>
			<?php if ($this->_tpl_vars['xoBlocks']['canvas_left_admin']): ?>
			<?php $this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme_name'])."/blockszone.html", 'smarty_include_vars' => array('blocks' => $this->_tpl_vars['xoBlocks']['canvas_left_admin'],'zoneClass' => 'xo-canvas-column','zoneId' => 'xo-canvas-leftcolumn')));
 ?>
			<?php endif; ?>
			<?php endif; ?>
			<!-- Center column / page -->
			<div id="xo-page">
				<!-- Top blocks -->
				<?php if ($this->_tpl_vars['xoBlocks']['page_topleft_admin'] || $this->_tpl_vars['xoBlocks']['page_topcenter_admin'] || $this->_tpl_vars['xoBlocks']['page_topright_admin']): ?>
				<div class="xo-blockszone xo-<?php echo $this->_tpl_vars['theme_top_order']; ?>
pageblocks" id="xo-page-topblocks">
					<?php $this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme_name'])."/centerblocks_admin.html", 'smarty_include_vars' => array('topbottom' => 'top','lcr' => ((is_array($_tmp=$this->_tpl_vars['theme_top_order'])) ? $this->_run_mod_handler('substr', true, $_tmp, 0, 1) : substr($_tmp, 0, 1)))));
 ?>
					<?php $this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme_name'])."/centerblocks_admin.html", 'smarty_include_vars' => array('topbottom' => 'top','lcr' => ((is_array($_tmp=$this->_tpl_vars['theme_top_order'])) ? $this->_run_mod_handler('substr', true, $_tmp, 1, 1) : substr($_tmp, 1, 1)))));
 ?>
					<?php $this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme_name'])."/centerblocks_admin.html", 'smarty_include_vars' => array('topbottom' => 'top','lcr' => ((is_array($_tmp=$this->_tpl_vars['theme_top_order'])) ? $this->_run_mod_handler('substr', true, $_tmp, 2, 1) : substr($_tmp, 2, 1)))));
 ?>
				</div>
				<?php endif; ?>
				<!-- Module content -->
				<?php if ($this->_tpl_vars['icms_contents']): ?>
				<div id="xo-content"><?php echo $this->_tpl_vars['icms_contents']; ?>
</div>
				<?php endif; ?>
				<!-- Bottom blocks -->
				<?php if ($this->_tpl_vars['xoBlocks']['page_bottomleft_admin'] || $this->_tpl_vars['xoBlocks']['page_bottomcenter_admin'] || $this->_tpl_vars['xoBlocks']['page_bottomright_admin']): ?>
				<div class="xo-blockszone xo-<?php echo $this->_tpl_vars['theme_bottom_order']; ?>
pageblocks" id="xo-page-bottomblocks">
					<?php $this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme_name'])."/centerblocks_admin.html", 'smarty_include_vars' => array('topbottom' => 'bottom','lcr' => ((is_array($_tmp=$this->_tpl_vars['theme_bottom_order'])) ? $this->_run_mod_handler('substr', true, $_tmp, 0, 1) : substr($_tmp, 0, 1)))));
 ?>
					<?php $this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme_name'])."/centerblocks_admin.html", 'smarty_include_vars' => array('topbottom' => 'bottom','lcr' => ((is_array($_tmp=$this->_tpl_vars['theme_bottom_order'])) ? $this->_run_mod_handler('substr', true, $_tmp, 1, 1) : substr($_tmp, 1, 1)))));
 ?>
					<?php $this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme_name'])."/centerblocks_admin.html", 'smarty_include_vars' => array('topbottom' => 'bottom','lcr' => ((is_array($_tmp=$this->_tpl_vars['theme_bottom_order'])) ? $this->_run_mod_handler('substr', true, $_tmp, 2, 1) : substr($_tmp, 2, 1)))));
 ?>
				</div>
				<?php endif; ?>
			</div>
			<!-- Right column -->
				<?php if ($this->_tpl_vars['icms_rtl']): ?>
			<?php if ($this->_tpl_vars['xoBlocks']['canvas_left_admin']): ?>
			<?php $this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme_name'])."/blockszone.html", 'smarty_include_vars' => array('blocks' => $this->_tpl_vars['xoBlocks']['canvas_left_admin'],'zoneClass' => 'xo-canvas-column','zoneId' => 'xo-canvas-leftcolumn')));
 ?>
			<?php endif; ?>
			<?php else: ?>
			<?php if ($this->_tpl_vars['xoBlocks']['canvas_right_admin']): ?>
			<?php $this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme_name'])."/blockszone.html", 'smarty_include_vars' => array('blocks' => $this->_tpl_vars['xoBlocks']['canvas_right_admin'],'zoneClass' => 'xo-canvas-column','zoneId' => 'xo-canvas-rightcolumn')));
 ?>
			<?php endif; ?>
			<?php endif; ?>
		</div>
	</div>
</div>
	<!-- Footer -->
	<div id="xo-footer">
	<?php echo $this->_tpl_vars['icms_footadm']; ?>

	</div>
	</div>
</body>
</html>
