<?php
/**
 * Test file to verify that include paths work correctly
 * This tests the core ImpressCMS include functionality
 */

header('Content-Type: application/json');

$results = [
    'message' => 'Testing ImpressCMS include paths',
    'working_directory' => getcwd(),
    'script_filename' => __FILE__,
    'tests' => []
];

// Test 1: Check if we can find mainfile.php
$mainfilePath = './mainfile.php';
$results['tests']['mainfile_exists'] = [
    'path' => $mainfilePath,
    'exists' => file_exists($mainfilePath),
    'readable' => file_exists($mainfilePath) ? is_readable($mainfilePath) : false
];

// Test 2: Check if we can find include/version.php
$versionPath = './include/version.php';
$results['tests']['version_exists'] = [
    'path' => $versionPath,
    'exists' => file_exists($versionPath),
    'readable' => file_exists($versionPath) ? is_readable($versionPath) : false
];

// Test 3: Check if we can find include/functions.php
$functionsPath = './include/functions.php';
$results['tests']['functions_exists'] = [
    'path' => $functionsPath,
    'exists' => file_exists($functionsPath),
    'readable' => file_exists($functionsPath) ? is_readable($functionsPath) : false
];

// Test 4: Check if we can find libraries/icms/Autoloader.php
$autoloaderPath = './libraries/icms/Autoloader.php';
$results['tests']['autoloader_exists'] = [
    'path' => $autoloaderPath,
    'exists' => file_exists($autoloaderPath),
    'readable' => file_exists($autoloaderPath) ? is_readable($autoloaderPath) : false
];

// Test 5: Try to include version.php if it exists
if (file_exists($versionPath)) {
    try {
        ob_start();
        include_once $versionPath;
        ob_end_clean();
        
        $results['tests']['version_include'] = [
            'success' => true,
            'constants_defined' => [
                'ICMS_VERSION_NAME' => defined('ICMS_VERSION_NAME'),
                'ICMS_VERSION_NUMBER' => defined('ICMS_VERSION_NUMBER')
            ]
        ];
        
        if (defined('ICMS_VERSION_NAME')) {
            $results['tests']['version_include']['version_name'] = ICMS_VERSION_NAME;
        }
        if (defined('ICMS_VERSION_NUMBER')) {
            $results['tests']['version_include']['version_number'] = ICMS_VERSION_NUMBER;
        }
        
    } catch (Exception $e) {
        $results['tests']['version_include'] = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
} else {
    $results['tests']['version_include'] = [
        'success' => false,
        'error' => 'version.php not found'
    ];
}

// Test 6: Check directory listing
$results['tests']['directory_contents'] = [];
if (is_dir('.')) {
    $files = scandir('.');
    $results['tests']['directory_contents'] = array_slice($files, 0, 10); // First 10 files
}

// Test 7: Check if we're in the right context
$results['context'] = [
    'current_dir' => getcwd(),
    'script_dir' => dirname(__FILE__),
    'expected_legacy_dir' => realpath('.'),
    'php_self' => $_SERVER['PHP_SELF'] ?? 'not set',
    'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'not set'
];

echo json_encode($results, JSON_PRETTY_PRINT);
?>
