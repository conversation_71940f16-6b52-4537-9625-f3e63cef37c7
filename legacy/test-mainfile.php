<?php
/**
 * Test file to verify that mainfile.php can be included properly
 * This simulates what most ImpressCMS pages do
 */

// This is what most ImpressCMS files do - include mainfile.php
// We'll test this carefully to see if it works

header('Content-Type: application/json');

$results = [
    'message' => 'Testing mainfile.php inclusion',
    'working_directory' => getcwd(),
    'tests' => []
];

// Test if mainfile.php exists and is readable
$mainfilePath = 'mainfile.php';
$results['tests']['mainfile_check'] = [
    'path' => $mainfilePath,
    'exists' => file_exists($mainfilePath),
    'readable' => file_exists($mainfilePath) ? is_readable($mainfilePath) : false
];

// Check what's in mainfile.php (first few lines)
if (file_exists($mainfilePath)) {
    $mainfileContent = file_get_contents($mainfilePath);
    $lines = explode("\n", $mainfileContent);
    $results['tests']['mainfile_content'] = [
        'first_10_lines' => array_slice($lines, 0, 10),
        'total_lines' => count($lines)
    ];
    
    // Check if it contains the installation redirect
    $hasInstallRedirect = strpos($mainfileContent, 'install/index.php') !== false;
    $results['tests']['mainfile_analysis'] = [
        'has_install_redirect' => $hasInstallRedirect,
        'size_bytes' => strlen($mainfileContent)
    ];
}

// Test if we can safely include version.php first (this should work)
try {
    if (file_exists('include/version.php')) {
        include_once 'include/version.php';
        $results['tests']['version_include'] = [
            'success' => true,
            'version_name' => defined('ICMS_VERSION_NAME') ? ICMS_VERSION_NAME : 'not defined'
        ];
    }
} catch (Exception $e) {
    $results['tests']['version_include'] = [
        'success' => false,
        'error' => $e->getMessage()
    ];
}

// Check if XOOPS_INSTALL is defined (this affects mainfile.php behavior)
$results['tests']['install_status'] = [
    'XOOPS_INSTALL_defined' => defined('XOOPS_INSTALL'),
    'value' => defined('XOOPS_INSTALL') ? XOOPS_INSTALL : 'not defined'
];

echo json_encode($results, JSON_PRETTY_PRINT);
?>
