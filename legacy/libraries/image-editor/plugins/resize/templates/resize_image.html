<script type="text/javascript">
  var resize_script_server_file = '<{$icms_lib_url}>/image-editor/plugins/resize/resize_image.php';
</script>
<script type="text/javascript" src="<{$icms_lib_url}>/image-editor/plugins/resize/resize_image.js"></script>
<form id="resize_form" name="resize_form">
  <table width="100%">
    <tr>
      <td width="20%"><b><{$smarty.const._WIDTH}>:</b></td>
      <td><input type="text" name="resize_width" id="resize_width" size="10" value="<{$image.width}>"></td>
    </tr>
    <tr>
      <td><b><{$smarty.const._HEIGHT}>:</b></td>
      <td><input type="text" name="resize_height" id="resize_height" size="10" value="<{$image.height}>"></td>
    </tr>
    <tr>
      <td colspan="2" align="center" id="cropButtonCell">
        <input type="button" onclick="resize_preview(this);" value="<{$smarty.const._RESIZE_PREVIEW}>" /> 
        <input type="button" onclick="resize_save(this);" value="<{$smarty.const._RESIZE_EXECUTE}>" />
        <input type="hidden" id="fimage_path" value="<{$image.path}>">
        <input type="hidden" id="fimage_url" value="<{$image.url}>">
      </td>
    </tr>
  </table>
</form>