<?php
/*************************************************************************************
 * mathematica.php
 * --------
 * Author: <PERSON> (<EMAIL>)
 * Copyright: (c) 2014 <PERSON> G<PERSON> (http://www.msu.edu/~glosser1)
 * Release Version: 1.0.8.13
 * Date Started: 2014/08/11
 *
 * Mathematica language file for GeSHi.
 *
 * COMMENTS
 * --------
 * Mathematica itself uses a lot of hyper-contextual syntax highlighting
 * that doesn't work well outside of their proprietary Notebook interface.
 * Consequently, external syntax highlighting tends to get noisy quickly.
 * Here, I've tried to create a triadic color scheme to distinguish keywords,
 * infix functions, and $-designated variables.
 *
 * CHANGES
 * -------
 * 2016/10/27 (1.0.8.13)
 *  - Update function list to Ver. 11
 *  - Add delimiters for associative arrays
 * 2014/10/13 (1.0.8.11)
 *  - Removed style variables for compatability
 * 2014/08/11 (1.0.8.11)
 *  - First Release
 *  - Symbols taken from http://reference.wolfram.com/language/guide/AlphabeticalListing.html
 *    (Mathematica Version 10)
 *
 * TODO (updated 2014/08/12)
 * -------------------------
 * - evaluate colored array syntax
 *************************************************************************************
 *
 *     This file is part of GeSHi.
 *
 *   GeSHi is free software; you can redistribute it and/or modify
 *   it under the terms of the GNU General Public License as published by
 *   the Free Software Foundation; either version 2 of the License, or
 *   (at your option) any later version.
 *
 *   GeSHi is distributed in the hope that it will be useful,
 *   but WITHOUT ANY WARRANTY; without even the implied warranty of
 *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *   GNU General Public License for more details.
 *
 *   You should have received a copy of the GNU General Public License
 *   along with GeSHi; if not, write to the Free Software
 *   Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 ************************************************************************************/
//"Base" styles
//string     = 'color: #666666; font-style: italic;';
//comment    = 'color: #999999; font-style: italic;';

//Purple-green colors
//primary    = 'color: #500D75; font-weight: bold;'; //keywords
//secondary  = 'color: #70A30A; font-weight: bold;'; //infix functions
//tertiary   = 'color: #8C0953; font-weight: bold;'; //$variables
//quaternary = 'color: #AFAF0B; font-weight: bold;'; //potentially array syntax in the future

//Orange-blue colors (similar to python coloring)
//primary    = 'color: #FF7700; font-weight: bold;'; //keywords
//secondary  = 'color: #133CAC; font-weight: bold;'; //infix functions
//tertiary   = 'color: #028E9B; font-weight: bold;'; //$variables
//quaternary = 'color: #FFAD00; font-weight: bold;'; //potentially array syntax in the future

$language_data = array(
    'LANG_NAME' => 'Mathematica',
    'COMMENT_SINGLE' => array(),
    'COMMENT_MULTI' => array(
        '(*' => '*)',
    ),
    'COMMENT_REGEXP' => array(),
    'CASE_KEYWORDS' => GESHI_CAPS_NO_CHANGE,
    'QUOTEMARKS' => array(
        1 => '"',
    ),
    'ESCAPE_CHAR' => '',
    'ESCAPE_REGEXP' => array(),
    'HARDQUOTE' => array(),
    'HARDESCAPE' => array(),
    'HARDCHAR' => '',
    'NUMBERS' =>
        GESHI_NUMBER_INT_BASIC | GESHI_NUMBER_OCT_PREFIX | GESHI_NUMBER_HEX_PREFIX |
        GESHI_NUMBER_FLT_SCI_ZERO,
    'KEYWORDS' => array(
        1 => array(
            'AASTriangle',
            'APIFunction',
            'ARCHProcess',
            'ARIMAProcess',
            'ARMAProcess',
            'ARProcess',
            'ASATriangle',
            'AbelianGroup',
            'Abort',
            'AbortKernels',
            'AbortProtect',
            'AbortScheduledTask',
            'Above',
            'Abs',
            'AbsArg',
            'AbsoluteCorrelation',
            'AbsoluteCorrelationFunction',
            'AbsoluteCurrentValue',
            'AbsoluteDashing',
            'AbsoluteFileName',
            'AbsoluteOptions',
            'AbsolutePointSize',
            'AbsoluteThickness',
            'AbsoluteTime',
            'AbsoluteTiming',
            'AccountingForm',
            'Accumulate',
            'Accuracy',
            'AccuracyGoal',
            'ActionMenu',
            'Activate',
            'ActiveStyle',
            'AcyclicGraphQ',
            'AddTo',
            'AddUsers',
            'AdjacencyGraph',
            'AdjacencyList',
            'AdjacencyMatrix',
            'AdjustTimeSeriesForecast',
            'AdjustmentBox',
            'AdjustmentBoxOptions',
            'AdministrativeDivisionData',
            'AffineHalfSpace',
            'AffineSpace',
            'AffineStateSpaceModel',
            'AffineTransform',
            'After',
            'AirPressureData',
            'AirTemperatureData',
            'AircraftData',
            'AirportData',
            'AiryAi',
            'AiryAiPrime',
            'AiryAiZero',
            'AiryBi',
            'AiryBiPrime',
            'AiryBiZero',
            'AlgebraicIntegerQ',
            'AlgebraicNumber',
            'AlgebraicNumberDenominator',
            'AlgebraicNumberNorm',
            'AlgebraicNumberPolynomial',
            'AlgebraicNumberTrace',
            'AlgebraicUnitQ',
            'Algebraics',
            'Alignment',
            'AlignmentPoint',
            'All',
            'AllTrue',
            'AllowGroupClose',
            'AllowInlineCells',
            'AllowLooseGrammar',
            'AllowReverseGroupClose',
            'AllowedDimensions',
            'AlphaChannel',
            'Alphabet',
            'AlphabeticOrder',
            'AlphabeticSort',
            'AlternateImage',
            'AlternatingFactorial',
            'AlternatingGroup',
            'AlternativeHypothesis',
            'Alternatives',
            'AltitudeMethod',
            'AmbiguityFunction',
            'AmbiguityList',
            'AnatomyData',
            'AnatomyForm',
            'AnatomyPlot3D',
            'AnchoredSearch',
            'And',
            'AndersonDarlingTest',
            'AngerJ',
            'AngleBracket',
            'AnglePath',
            'AngleVector',
            'AngularGauge',
            'Animate',
            'AnimationDirection',
            'AnimationRate',
            'AnimationRepetitions',
            'AnimationRunTime',
            'AnimationRunning',
            'AnimationTimeIndex',
            'Animator',
            'Annotation',
            'Annuity',
            'AnnuityDue',
            'Annulus',
            'Anonymous',
            'Antialiasing',
            'AntihermitianMatrixQ',
            'Antisymmetric',
            'AntisymmetricMatrixQ',
            'AnyOrder',
            'AnySubset',
            'AnyTrue',
            'Apart',
            'ApartSquareFree',
            'Appearance',
            'AppearanceElements',
            'AppearanceRules',
            'AppellF1',
            'Append',
            'AppendTo',
            'Apply',
            'ArcCos',
            'ArcCosh',
            'ArcCot',
            'ArcCoth',
            'ArcCsc',
            'ArcCsch',
            'ArcCurvature',
            'ArcLength',
            'ArcSec',
            'ArcSech',
            'ArcSin',
            'ArcSinDistribution',
            'ArcSinh',
            'ArcTan',
            'ArcTanh',
            'Area',
            'Arg',
            'ArgMax',
            'ArgMin',
            'ArithmeticGeometricMean',
            'Array',
            'ArrayComponents',
            'ArrayDepth',
            'ArrayFilter',
            'ArrayFlatten',
            'ArrayMesh',
            'ArrayPad',
            'ArrayPlot',
            'ArrayQ',
            'ArrayResample',
            'ArrayReshape',
            'ArrayRules',
            'Arrays',
            'Arrow',
            'Arrowheads',
            'Ask',
            'AskAppend',
            'AskConfirm',
            'AskDisplay',
            'AskFunction',
            'AskTemplateDisplay',
            'AskedQ',
            'AskedValue',
            'AspectRatio',
            'Assert',
            'AssociateTo',
            'Association',
            'AssociationFormat',
            'AssociationMap',
            'AssociationQ',
            'AssociationThread',
            'AssumeDeterministic',
            'Assuming',
            'Assumptions',
            'AsymptoticOutputTracker',
            'Asynchronous',
            'AsynchronousTaskObject',
            'AsynchronousTasks',
            'AtomQ',
            'Attributes',
            'Audio',
            'AudioAmplify',
            'AudioBlockMap',
            'AudioChannelCombine',
            'AudioChannelMix',
            'AudioChannelSeparate',
            'AudioChannels',
            'AudioData',
            'AudioDelay',
            'AudioDelete',
            'AudioDevice',
            'AudioFade',
            'AudioFrequencyShift',
            'AudioGenerator',
            'AudioIntervals',
            'AudioJoin',
            'AudioLabel',
            'AudioLength',
            'AudioLocalMeasurements',
            'AudioMeasurements',
            'AudioNormalize',
            'AudioOverlay',
            'AudioPad',
            'AudioPan',
            'AudioPartition',
            'AudioPitchShift',
            'AudioPlot',
            'AudioQ',
            'AudioResample',
            'AudioReverb',
            'AudioSampleRate',
            'AudioSplit',
            'AudioTimeStretch',
            'AudioTrim',
            'AudioType',
            'AugmentedSymmetricPolynomial',
            'Authentication',
            'AutoAction',
            'AutoDelete',
            'AutoIndent',
            'AutoItalicWords',
            'AutoMultiplicationSymbol',
            'AutoRefreshed',
            'AutoRemove',
            'AutoScroll',
            'AutoSpacing',
            'AutoSubmitting',
            'Autocomplete',
            'AutocompletionFunction',
            'AutocorrelationTest',
            'Automatic',
            'AutorunSequencing',
            'Axes',
            'AxesEdge',
            'AxesLabel',
            'AxesOrigin',
            'AxesStyle',
            'Axis',
            'BSplineBasis',
            'BSplineCurve',
            'BSplineFunction',
            'BSplineSurface',
            'BabyMonsterGroupB',
            'Back',
            'Background',
            'Backslash',
            'Backward',
            'Ball',
            'Band',
            'BandpassFilter',
            'BandstopFilter',
            'BarChart',
            'BarChart3D',
            'BarLegend',
            'BarOrigin',
            'BarSpacing',
            'BarabasiAlbertGraphDistribution',
            'BarcodeImage',
            'BarcodeRecognize',
            'BaringhausHenzeTest',
            'BarlowProschanImportance',
            'BarnesG',
            'BartlettHannWindow',
            'BartlettWindow',
            'BaseForm',
            'BaseStyle',
            'Baseline',
            'BaselinePosition',
            'BatchNormalizationLayer',
            'BatchSize',
            'BatesDistribution',
            'BattleLemarieWavelet',
            'BayesianMaximization',
            'BayesianMaximizationObject',
            'BayesianMinimization',
            'BayesianMinimizationObject',
            'Because',
            'BeckmannDistribution',
            'Beep',
            'Before',
            'Begin',
            'BeginDialogPacket',
            'BeginPackage',
            'BellB',
            'BellY',
            'Below',
            'BenfordDistribution',
            'BeniniDistribution',
            'BenktanderGibratDistribution',
            'BenktanderWeibullDistribution',
            'BernoulliB',
            'BernoulliDistribution',
            'BernoulliGraphDistribution',
            'BernoulliProcess',
            'BernsteinBasis',
            'BesselFilterModel',
            'BesselI',
            'BesselJ',
            'BesselJZero',
            'BesselK',
            'BesselY',
            'BesselYZero',
            'Beta',
            'BetaBinomialDistribution',
            'BetaDistribution',
            'BetaNegativeBinomialDistribution',
            'BetaPrimeDistribution',
            'BetaRegularized',
            'Between',
            'BetweennessCentrality',
            'BezierCurve',
            'BezierFunction',
            'BilateralFilter',
            'BinCounts',
            'BinLists',
            'Binarize',
            'BinaryDistance',
            'BinaryFormat',
            'BinaryImageQ',
            'BinaryRead',
            'BinaryReadList',
            'BinaryWrite',
            'Binomial',
            'BinomialDistribution',
            'BinomialProcess',
            'BinormalDistribution',
            'BiorthogonalSplineWavelet',
            'BipartiteGraphQ',
            'BiquadraticFilterModel',
            'BirnbaumImportance',
            'BirnbaumSaundersDistribution',
            'BitAnd',
            'BitClear',
            'BitGet',
            'BitLength',
            'BitNot',
            'BitOr',
            'BitSet',
            'BitShiftLeft',
            'BitShiftRight',
            'BitXor',
            'Black',
            'BlackmanHarrisWindow',
            'BlackmanNuttallWindow',
            'BlackmanWindow',
            'Blank',
            'BlankNullSequence',
            'BlankSequence',
            'Blend',
            'Block',
            'BlockMap',
            'BlockRandom',
            'BlomqvistBeta',
            'BlomqvistBetaTest',
            'Blue',
            'Blur',
            'BodePlot',
            'BohmanWindow',
            'Bold',
            'Bookmarks',
            'Boole',
            'BooleanConsecutiveFunction',
            'BooleanConvert',
            'BooleanCountingFunction',
            'BooleanFunction',
            'BooleanGraph',
            'BooleanMaxterms',
            'BooleanMinimize',
            'BooleanMinterms',
            'BooleanQ',
            'BooleanRegion',
            'BooleanStrings',
            'BooleanTable',
            'BooleanVariables',
            'Booleans',
            'BorderDimensions',
            'BorelTannerDistribution',
            'Bottom',
            'BottomHatTransform',
            'BoundaryDiscretizeGraphics',
            'BoundaryDiscretizeRegion',
            'BoundaryMesh',
            'BoundaryMeshRegion',
            'BoundaryMeshRegionQ',
            'BoundaryStyle',
            'BoundedRegionQ',
            'BoundingRegion',
            'BoxData',
            'BoxMatrix',
            'BoxObject',
            'BoxRatios',
            'BoxStyle',
            'BoxWhiskerChart',
            'Boxed',
            'BracketingBar',
            'BrayCurtisDistance',
            'BreadthFirstScan',
            'Break',
            'BridgeData',
            'BroadcastStationData',
            'Brown',
            'BrownForsytheTest',
            'BrownianBridgeProcess',
            'BubbleChart',
            'BubbleChart3D',
            'BubbleScale',
            'BubbleSizes',
            'BuildingData',
            'BulletGauge',
            'BusinessDayQ',
            'ButterflyGraph',
            'ButterworthFilterModel',
            'Button',
            'ButtonBar',
            'ButtonBox',
            'ButtonBoxOptions',
            'ButtonData',
            'ButtonFunction',
            'ButtonMinHeight',
            'ButtonNotebook',
            'ButtonSource',
            'Byte',
            'ByteArray',
            'ByteArrayQ',
            'ByteCount',
            'ByteOrdering',

            'C',
            'CDF',
            'CDFDeploy',
            'CDFInformation',
            'CDFWavelet',
            'CForm',
            'CMYKColor',
            'CachePersistence',
            'CalendarConvert',
            'CalendarData',
            'CalendarType',
            'CallPacket',
            'Callout',
            'CalloutMarker',
            'CalloutStyle',
            'CanberraDistance',
            'Cancel',
            'CancelButton',
            'CandlestickChart',
            'CanonicalGraph',
            'CanonicalName',
            'CanonicalWarpingCorrespondence',
            'CanonicalWarpingDistance',
            'CantorStaircase',
            'Cap',
            'CapForm',
            'CapitalDifferentialD',
            'Capitalize',
            'CapsuleShape',
            'CarlemanLinearize',
            'CarmichaelLambda',
            'CaseOrdering',
            'CaseSensitive',
            'Cases',
            'Cashflow',
            'Casoratian',
            'Catalan',
            'CatalanNumber',
            'Catch',
            'Catenate',
            'CatenateLayer',
            'CauchyDistribution',
            'CauchyWindow',
            'CayleyGraph',
            'Ceiling',
            'CelestialSystem',
            'Cell',
            'CellAutoOverwrite',
            'CellBaseline',
            'CellBracketOptions',
            'CellChangeTimes',
            'CellContext',
            'CellDingbat',
            'CellDynamicExpression',
            'CellEditDuplicate',
            'CellEpilog',
            'CellEvaluationDuplicate',
            'CellEvaluationFunction',
            'CellEventActions',
            'CellFrame',
            'CellFrameColor',
            'CellFrameLabelMargins',
            'CellFrameLabels',
            'CellFrameMargins',
            'CellGroup',
            'CellGroupData',
            'CellGrouping',
            'CellID',
            'CellLabel',
            'CellLabelAutoDelete',
            'CellMargins',
            'CellObject',
            'CellOpen',
            'CellPrint',
            'CellProlog',
            'CellStyle',
            'CellTags',
            'Cells',
            'CellularAutomaton',
            'CensoredDistribution',
            'Censoring',
            'Center',
            'CenterArray',
            'CenterDot',
            'CentralMoment',
            'CentralMomentGeneratingFunction',
            'ChampernowneNumber',
            'ChanVeseBinarize',
            'ChannelBase',
            'ChannelDatabin',
            'ChannelListen',
            'ChannelListener',
            'ChannelListeners',
            'ChannelObject',
            'ChannelPreSendFunction',
            'ChannelSend',
            'ChannelSubscribers',
            'Character',
            'CharacterCounts',
            'CharacterEncoding',
            'CharacterName',
            'CharacterRange',
            'CharacteristicFunction',
            'CharacteristicPolynomial',
            'Characters',
            'ChartBaseStyle',
            'ChartElementFunction',
            'ChartElements',
            'ChartLabels',
            'ChartLayout',
            'ChartLegends',
            'ChartStyle',
            'Chebyshev1FilterModel',
            'Chebyshev2FilterModel',
            'ChebyshevT',
            'ChebyshevU',
            'Check',
            'CheckAbort',
            'Checkbox',
            'CheckboxBar',
            'ChemicalData',
            'ChessboardDistance',
            'ChiDistribution',
            'ChiSquareDistribution',
            'ChineseRemainder',
            'ChoiceButtons',
            'ChoiceDialog',
            'CholeskyDecomposition',
            'Chop',
            'ChromaticPolynomial',
            'ChromaticityPlot',
            'ChromaticityPlot3D',
            'Circle',
            'CircleDot',
            'CircleMinus',
            'CirclePlus',
            'CirclePoints',
            'CircleTimes',
            'CirculantGraph',
            'CircularOrthogonalMatrixDistribution',
            'CircularQuaternionMatrixDistribution',
            'CircularRealMatrixDistribution',
            'CircularSymplecticMatrixDistribution',
            'CircularUnitaryMatrixDistribution',
            'Circumsphere',
            'CityData',
            'ClassPriors',
            'ClassifierFunction',
            'ClassifierInformation',
            'ClassifierMeasurements',
            'ClassifierMeasurementsObject',
            'Classify',
            'Clear',
            'ClearAll',
            'ClearAttributes',
            'ClearCookies',
            'ClearPermissions',
            'ClearSystemCache',
            'ClebschGordan',
            'ClickPane',
            'Clip',
            'ClipPlanes',
            'ClipPlanesStyle',
            'ClipRange',
            'ClippingStyle',
            'Clock',
            'ClockGauge',
            'Close',
            'CloseKernels',
            'ClosenessCentrality',
            'Closing',
            'CloudAccountData',
            'CloudBase',
            'CloudConnect',
            'CloudDeploy',
            'CloudDirectory',
            'CloudDisconnect',
            'CloudEvaluate',
            'CloudExport',
            'CloudExpression',
            'CloudExpressions',
            'CloudFunction',
            'CloudGet',
            'CloudImport',
            'CloudLoggingData',
            'CloudObject',
            'CloudObjects',
            'CloudPublish',
            'CloudPut',
            'CloudSave',
            'CloudShare',
            'CloudSubmit',
            'CloudSymbol',
            'ClusterClassify',
            'ClusterDissimilarityFunction',
            'ClusteringComponents',
            'ClusteringTree',
            'CodeAssistOptions',
            'Coefficient',
            'CoefficientArrays',
            'CoefficientList',
            'CoefficientRules',
            'CoifletWavelet',
            'Collect',
            'Colon',
            'ColorBalance',
            'ColorCombine',
            'ColorConvert',
            'ColorCoverage',
            'ColorData',
            'ColorDataFunction',
            'ColorDistance',
            'ColorFunction',
            'ColorFunctionScaling',
            'ColorNegate',
            'ColorProfileData',
            'ColorQ',
            'ColorQuantize',
            'ColorReplace',
            'ColorRules',
            'ColorSeparate',
            'ColorSetter',
            'ColorSlider',
            'ColorSpace',
            'ColorToneMapping',
            'Colorize',
            'Column',
            'ColumnAlignments',
            'ColumnLines',
            'ColumnSpacings',
            'ColumnWidths',
            'ColumnsEqual',
            'CombinerFunction',
            'CometData',
            'CommonName',
            'CommonUnits',
            'Commonest',
            'CommonestFilter',
            'CommunityBoundaryStyle',
            'CommunityGraphPlot',
            'CommunityLabels',
            'CommunityRegionStyle',
            'CompanyData',
            'CompatibleUnitQ',
            'CompilationOptions',
            'CompilationTarget',
            'Compile',
            'Compiled',
            'CompiledFunction',
            'Complement',
            'CompleteGraph',
            'CompleteGraphQ',
            'CompleteKaryTree',
            'Complex',
            'ComplexExpand',
            'ComplexInfinity',
            'Complexes',
            'ComplexityFunction',
            'ComponentMeasurements',
            'ComposeList',
            'ComposeSeries',
            'CompositeQ',
            'Composition',
            'CompoundElement',
            'CompoundExpression',
            'CompoundPoissonDistribution',
            'CompoundPoissonProcess',
            'CompoundRenewalProcess',
            'Compress',
            'Condition',
            'ConditionalExpression',
            'Conditioned',
            'Cone',
            'ConfidenceLevel',
            'ConfidenceRange',
            'ConfidenceTransform',
            'ConformAudio',
            'ConformImages',
            'Congruent',
            'ConicHullRegion',
            'Conjugate',
            'ConjugateTranspose',
            'Conjunction',
            'ConnectLibraryCallbackFunction',
            'ConnectedComponents',
            'ConnectedGraphComponents',
            'ConnectedGraphQ',
            'ConnectedMeshComponents',
            'ConnesWindow',
            'ConoverTest',
            'Constant',
            'ConstantArray',
            'ConstantImage',
            'ConstantRegionQ',
            'Constants',
            'ConstellationData',
            'Containing',
            'ContainsAll',
            'ContainsAny',
            'ContainsExactly',
            'ContainsNone',
            'ContainsOnly',
            'ContentFieldOptions',
            'ContentLocationFunction',
            'ContentObject',
            'ContentPadding',
            'ContentSelectable',
            'ContentSize',
            'Context',
            'ContextToFileName',
            'Contexts',
            'Continue',
            'ContinuedFraction',
            'ContinuedFractionK',
            'ContinuousAction',
            'ContinuousMarkovProcess',
            'ContinuousTask',
            'ContinuousTimeModelQ',
            'ContinuousWaveletData',
            'ContinuousWaveletTransform',
            'ContourDetect',
            'ContourLabels',
            'ContourPlot',
            'ContourPlot3D',
            'ContourShading',
            'ContourStyle',
            'Contours',
            'ContraharmonicMean',
            'Control',
            'ControlActive',
            'ControlPlacement',
            'ControlType',
            'ControllabilityGramian',
            'ControllabilityMatrix',
            'ControllableDecomposition',
            'ControllableModelQ',
            'ControllerInformation',
            'ControllerLinking',
            'ControllerManipulate',
            'ControllerMethod',
            'ControllerPath',
            'ControllerState',
            'ControlsRendering',
            'Convergents',
            'ConversionRules',
            'ConvexHullMesh',
            'ConvolutionLayer',
            'Convolve',
            'ConwayGroupCo1',
            'ConwayGroupCo2',
            'ConwayGroupCo3',
            'CookieFunction',
            'CoordinateBoundingBox',
            'CoordinateBoundingBoxArray',
            'CoordinateBounds',
            'CoordinateBoundsArray',
            'CoordinateChartData',
            'CoordinateTransform',
            'CoordinateTransformData',
            'CoordinatesToolOptions',
            'CoprimeQ',
            'Coproduct',
            'CopulaDistribution',
            'CopyDatabin',
            'CopyDirectory',
            'CopyFile',
            'CopyToClipboard',
            'Copyable',
            'CornerFilter',
            'CornerNeighbors',
            'Correlation',
            'CorrelationDistance',
            'CorrelationFunction',
            'CorrelationTest',
            'Cos',
            'CosIntegral',
            'Cosh',
            'CoshIntegral',
            'CosineDistance',
            'CosineWindow',
            'Cot',
            'Coth',
            'Count',
            'CountDistinct',
            'CountDistinctBy',
            'CountRoots',
            'CountryData',
            'Counts',
            'CountsBy',
            'Covariance',
            'CovarianceEstimatorFunction',
            'CovarianceFunction',
            'CoxIngersollRossProcess',
            'CoxModel',
            'CoxModelFit',
            'CoxianDistribution',
            'CramerVonMisesTest',
            'CreateArchive',
            'CreateCellID',
            'CreateChannel',
            'CreateCloudExpression',
            'CreateDatabin',
            'CreateDialog',
            'CreateDirectory',
            'CreateDocument',
            'CreateFile',
            'CreateIntermediateDirectories',
            'CreateManagedLibraryExpression',
            'CreateNotebook',
            'CreatePalette',
            'CreatePermissionsGroup',
            'CreateScheduledTask',
            'CreateSearchIndex',
            'CreateUUID',
            'CreateWindow',
            'CriterionFunction',
            'CriticalSection',
            'CriticalityFailureImportance',
            'CriticalitySuccessImportance',
            'Cross',
            'CrossEntropyLossLayer',
            'CrossMatrix',
            'CrossingDetect',
            'Csc',
            'Csch',
            'CubeRoot',
            'Cubics',
            'Cuboid',
            'Cumulant',
            'CumulantGeneratingFunction',
            'Cup',
            'CupCap',
            'Curl',
            'CurrencyConvert',
            'CurrentImage',
            'CurrentValue',
            'CurvatureFlowFilter',
            'CurveClosed',
            'Cyan',
            'CycleGraph',
            'CycleIndexPolynomial',
            'Cycles',
            'CyclicGroup',
            'Cyclotomic',
            'Cylinder',
            'CylindricalDecomposition',

            'D',
            'DEigensystem',
            'DEigenvalues',
            'DGaussianWavelet',
            'DMSList',
            'DMSString',
            'DSolve',
            'DSolveValue',
            'DagumDistribution',
            'DamData',
            'DamerauLevenshteinDistance',
            'Darker',
            'Dashed',
            'Dashing',
            'DataDistribution',
            'DataRange',
            'DataReversed',
            'Databin',
            'DatabinAdd',
            'DatabinRemove',
            'DatabinUpload',
            'Databins',
            'Dataset',
            'DateBounds',
            'DateDifference',
            'DateFormat',
            'DateFunction',
            'DateHistogram',
            'DateList',
            'DateListLogPlot',
            'DateListPlot',
            'DateListStepPlot',
            'DateObject',
            'DateObjectQ',
            'DatePattern',
            'DatePlus',
            'DateRange',
            'DateReduction',
            'DateString',
            'DateTicksFormat',
            'DateValue',
            'Dated',
            'DatedUnit',
            'DaubechiesWavelet',
            'DavisDistribution',
            'DawsonF',
            'DayCount',
            'DayCountConvention',
            'DayHemisphere',
            'DayMatchQ',
            'DayName',
            'DayNightTerminator',
            'DayPlus',
            'DayRange',
            'DayRound',
            'DaylightQ',
            'DeBruijnGraph',
            'Decapitalize',
            'DeclarePackage',
            'Decompose',
            'DeconvolutionLayer',
            'Decrement',
            'Decrypt',
            'DedekindEta',
            'DeepSpaceProbeData',
            'Default',
            'DefaultAxesStyle',
            'DefaultBaseStyle',
            'DefaultBoxStyle',
            'DefaultButton',
            'DefaultDuplicateCellStyle',
            'DefaultDuration',
            'DefaultElement',
            'DefaultFaceGridsStyle',
            'DefaultFieldHintStyle',
            'DefaultFrameStyle',
            'DefaultFrameTicksStyle',
            'DefaultGridLinesStyle',
            'DefaultLabelStyle',
            'DefaultMenuStyle',
            'DefaultNaturalLanguage',
            'DefaultNewCellStyle',
            'DefaultOptions',
            'DefaultTicksStyle',
            'DefaultTooltipStyle',
            'Defer',
            'DefineInputStreamMethod',
            'DefineOutputStreamMethod',
            'Definition',
            'Degree',
            'DegreeCentrality',
            'DegreeGraphDistribution',
            'Deinitialization',
            'Del',
            'DelaunayMesh',
            'Delayed',
            'Deletable',
            'Delete',
            'DeleteBorderComponents',
            'DeleteCases',
            'DeleteChannel',
            'DeleteCloudExpression',
            'DeleteContents',
            'DeleteDirectory',
            'DeleteDuplicates',
            'DeleteDuplicatesBy',
            'DeleteFile',
            'DeleteMissing',
            'DeletePermissionsKey',
            'DeleteSearchIndex',
            'DeleteSmallComponents',
            'DeleteStopwords',
            'DelimitedSequence',
            'Delimiter',
            'DelimiterFlashTime',
            'Delimiters',
            'DeliveryFunction',
            'Dendrogram',
            'Denominator',
            'DensityHistogram',
            'DensityPlot',
            'DensityPlot3D',
            'DependentVariables',
            'Deploy',
            'Deployed',
            'Depth',
            'DepthFirstScan',
            'Derivative',
            'DerivativeFilter',
            'DescriptorStateSpace',
            'DesignMatrix',
            'Det',
            'DeviceClose',
            'DeviceConfigure',
            'DeviceExecute',
            'DeviceExecuteAsynchronous',
            'DeviceObject',
            'DeviceOpen',
            'DeviceRead',
            'DeviceReadBuffer',
            'DeviceReadLatest',
            'DeviceReadList',
            'DeviceReadTimeSeries',
            'DeviceStreams',
            'DeviceWrite',
            'DeviceWriteBuffer',
            'Devices',
            'Diagonal',
            'DiagonalMatrix',
            'DiagonalizableMatrixQ',
            'Dialog',
            'DialogInput',
            'DialogNotebook',
            'DialogProlog',
            'DialogReturn',
            'DialogSymbols',
            'Diamond',
            'DiamondMatrix',
            'DiceDissimilarity',
            'DictionaryLookup',
            'DictionaryWordQ',
            'DifferenceDelta',
            'DifferenceQuotient',
            'DifferenceRoot',
            'DifferenceRootReduce',
            'Differences',
            'DifferentialD',
            'DifferentialRoot',
            'DifferentialRootReduce',
            'DifferentiatorFilter',
            'DigitBlock',
            'DigitCharacter',
            'DigitCount',
            'DigitQ',
            'DihedralGroup',
            'Dilation',
            'DimensionReduce',
            'DimensionReducerFunction',
            'DimensionReduction',
            'DimensionalCombinations',
            'DimensionalMeshComponents',
            'Dimensions',
            'DiracComb',
            'DiracDelta',
            'DirectedEdge',
            'DirectedEdges',
            'DirectedGraph',
            'DirectedGraphQ',
            'DirectedInfinity',
            'Direction',
            'Directive',
            'Directory',
            'DirectoryName',
            'DirectoryQ',
            'DirectoryStack',
            'DirichletBeta',
            'DirichletCharacter',
            'DirichletCondition',
            'DirichletConvolve',
            'DirichletDistribution',
            'DirichletEta',
            'DirichletL',
            'DirichletLambda',
            'DirichletTransform',
            'DirichletWindow',
            'DisableFormatting',
            'DiscreteChirpZTransform',
            'DiscreteConvolve',
            'DiscreteDelta',
            'DiscreteHadamardTransform',
            'DiscreteIndicator',
            'DiscreteLQEstimatorGains',
            'DiscreteLQRegulatorGains',
            'DiscreteLyapunovSolve',
            'DiscreteMarkovProcess',
            'DiscretePlot',
            'DiscretePlot3D',
            'DiscreteRatio',
            'DiscreteRiccatiSolve',
            'DiscreteShift',
            'DiscreteTimeModelQ',
            'DiscreteUniformDistribution',
            'DiscreteVariables',
            'DiscreteWaveletData',
            'DiscreteWaveletPacketTransform',
            'DiscreteWaveletTransform',
            'DiscretizeGraphics',
            'DiscretizeRegion',
            'Discriminant',
            'DisjointQ',
            'Disjunction',
            'Disk',
            'DiskMatrix',
            'DiskSegment',
            'Dispatch',
            'DispersionEstimatorFunction',
            'DisplayAllSteps',
            'DisplayEndPacket',
            'DisplayForm',
            'DisplayFunction',
            'DisplayPacket',
            'DistanceFunction',
            'DistanceMatrix',
            'DistanceTransform',
            'Distribute',
            'DistributeDefinitions',
            'Distributed',
            'DistributedContexts',
            'DistributionChart',
            'DistributionFitTest',
            'DistributionParameterAssumptions',
            'DistributionParameterQ',
            'Dithering',
            'Div',
            'Divide',
            'DivideBy',
            'Dividers',
            'Divisible',
            'DivisorSigma',
            'DivisorSum',
            'Divisors',
            'Do',
            'DockedCells',
            'DocumentGenerator',
            'DocumentGeneratorInformation',
            'DocumentGenerators',
            'DocumentNotebook',
            'DominantColors',
            'Dot',
            'DotDashed',
            'DotEqual',
            'DotPlusLayer',
            'Dotted',
            'DoubleBracketingBar',
            'DoubleDownArrow',
            'DoubleLeftArrow',
            'DoubleLeftRightArrow',
            'DoubleLeftTee',
            'DoubleLongLeftArrow',
            'DoubleLongLeftRightArrow',
            'DoubleLongRightArrow',
            'DoubleRightArrow',
            'DoubleRightTee',
            'DoubleUpArrow',
            'DoubleUpDownArrow',
            'DoubleVerticalBar',
            'DownArrow',
            'DownArrowBar',
            'DownArrowUpArrow',
            'DownLeftRightVector',
            'DownLeftTeeVector',
            'DownLeftVector',
            'DownLeftVectorBar',
            'DownRightTeeVector',
            'DownRightVector',
            'DownRightVectorBar',
            'DownTee',
            'DownTeeArrow',
            'DownValues',
            'Downsample',
            'Drop',
            'DropoutLayer',
            'Dt',
            'DualSystemsModel',
            'DumpSave',
            'DuplicateFreeQ',
            'Duration',
            'Dynamic',
            'DynamicEvaluationTimeout',
            'DynamicGeoGraphics',
            'DynamicImage',
            'DynamicModule',
            'DynamicModuleValues',
            'DynamicSetting',
            'DynamicWrapper',

            'E',
            'EarthImpactData',
            'EarthquakeData',
            'EccentricityCentrality',
            'Echo',
            'EchoFunction',
            'EclipseType',
            'EdgeAdd',
            'EdgeBetweennessCentrality',
            'EdgeCapacity',
            'EdgeConnectivity',
            'EdgeContract',
            'EdgeCost',
            'EdgeCount',
            'EdgeCoverQ',
            'EdgeCycleMatrix',
            'EdgeDelete',
            'EdgeDetect',
            'EdgeForm',
            'EdgeIndex',
            'EdgeLabelStyle',
            'EdgeLabeling',
            'EdgeLabels',
            'EdgeList',
            'EdgeQ',
            'EdgeRenderingFunction',
            'EdgeRules',
            'EdgeShapeFunction',
            'EdgeStyle',
            'EdgeWeight',
            'EditDistance',
            'Editable',
            'EffectiveInterest',
            'Eigensystem',
            'Eigenvalues',
            'EigenvectorCentrality',
            'Eigenvectors',
            'Element',
            'ElementData',
            'ElementwiseLayer',
            'ElidedForms',
            'Eliminate',
            'Ellipsoid',
            'EllipticE',
            'EllipticExp',
            'EllipticExpPrime',
            'EllipticF',
            'EllipticFilterModel',
            'EllipticK',
            'EllipticLog',
            'EllipticNomeQ',
            'EllipticPi',
            'EllipticTheta',
            'EllipticThetaPrime',
            'EmbedCode',
            'EmbeddedHTML',
            'EmbeddedService',
            'EmbeddingLayer',
            'EmitSound',
            'EmpiricalDistribution',
            'EmptyGraphQ',
            'EmptyRegion',
            'Enabled',
            'Encode',
            'Encrypt',
            'EncryptedObject',
            'End',
            'EndDialogPacket',
            'EndOfBuffer',
            'EndOfFile',
            'EndOfLine',
            'EndOfString',
            'EndPackage',
            'EngineeringForm',
            'EnterExpressionPacket',
            'EnterTextPacket',
            'Entity',
            'EntityClass',
            'EntityClassList',
            'EntityCopies',
            'EntityGroup',
            'EntityInstance',
            'EntityList',
            'EntityProperties',
            'EntityProperty',
            'EntityPropertyClass',
            'EntityStore',
            'EntityTypeName',
            'EntityValue',
            'Entropy',
            'EntropyFilter',
            'Environment',
            'Epilog',
            'EpilogFunction',
            'Equal',
            'EqualTilde',
            'EqualTo',
            'Equilibrium',
            'EquirippleFilterKernel',
            'Equivalent',
            'Erf',
            'Erfc',
            'Erfi',
            'ErlangB',
            'ErlangC',
            'ErlangDistribution',
            'Erosion',
            'ErrorBox',
            'EscapeRadius',
            'EstimatedBackground',
            'EstimatedDistribution',
            'EstimatedProcess',
            'EstimatorGains',
            'EstimatorRegulator',
            'EuclideanDistance',
            'EulerAngles',
            'EulerE',
            'EulerGamma',
            'EulerMatrix',
            'EulerPhi',
            'EulerianGraphQ',
            'Evaluatable',
            'Evaluate',
            'EvaluatePacket',
            'EvaluateScheduledTask',
            'EvaluationBox',
            'EvaluationCell',
            'EvaluationData',
            'EvaluationElements',
            'EvaluationMonitor',
            'EvaluationNotebook',
            'EvaluationObject',
            'Evaluator',
            'EvenQ',
            'EventData',
            'EventHandler',
            'EventLabels',
            'EventSeries',
            'ExactBlackmanWindow',
            'ExactNumberQ',
            'ExampleData',
            'Except',
            'ExcludePods',
            'ExcludedForms',
            'ExcludedLines',
            'ExcludedPhysicalQuantities',
            'Exclusions',
            'ExclusionsStyle',
            'Exists',
            'Exit',
            'ExoplanetData',
            'Exp',
            'ExpGammaDistribution',
            'ExpIntegralE',
            'ExpIntegralEi',
            'ExpToTrig',
            'Expand',
            'ExpandAll',
            'ExpandDenominator',
            'ExpandFileName',
            'ExpandNumerator',
            'Expectation',
            'Exponent',
            'ExponentFunction',
            'ExponentStep',
            'ExponentialDistribution',
            'ExponentialFamily',
            'ExponentialGeneratingFunction',
            'ExponentialMovingAverage',
            'ExponentialPowerDistribution',
            'Export',
            'ExportForm',
            'ExportString',
            'Expression',
            'ExpressionCell',
            'ExtendedGCD',
            'Extension',
            'ExtentElementFunction',
            'ExtentMarkers',
            'ExtentSize',
            'ExternalBundle',
            'ExternalOptions',
            'ExternalTypeSignature',
            'Extract',
            'ExtractArchive',
            'ExtremeValueDistribution',

            'FARIMAProcess',
            'FRatioDistribution',
            'FaceForm',
            'FaceGrids',
            'FaceGridsStyle',
            'Factor',
            'FactorInteger',
            'FactorList',
            'FactorSquareFree',
            'FactorSquareFreeList',
            'FactorTerms',
            'FactorTermsList',
            'Factorial',
            'Factorial2',
            'FactorialMoment',
            'FactorialMomentGeneratingFunction',
            'FactorialPower',
            'Failure',
            'FailureAction',
            'FailureDistribution',
            'FailureQ',
            'False',
            'FareySequence',
            'FeatureDistance',
            'FeatureExtract',
            'FeatureExtraction',
            'FeatureExtractor',
            'FeatureExtractorFunction',
            'FeatureNames',
            'FeatureTypes',
            'FeedbackLinearize',
            'FeedbackSector',
            'FeedbackSectorStyle',
            'FeedbackType',
            'FetalGrowthData',
            'Fibonacci',
            'Fibonorial',
            'FieldHint',
            'FieldHintStyle',
            'FieldMasked',
            'FieldSize',
            'File',
            'FileBaseName',
            'FileByteCount',
            'FileDate',
            'FileExistsQ',
            'FileExtension',
            'FileFormat',
            'FileHash',
            'FileNameDepth',
            'FileNameDrop',
            'FileNameForms',
            'FileNameJoin',
            'FileNameSetter',
            'FileNameSplit',
            'FileNameTake',
            'FileNames',
            'FilePrint',
            'FileSystemMap',
            'FileSystemScan',
            'FileTemplate',
            'FileTemplateApply',
            'FileType',
            'FilledCurve',
            'Filling',
            'FillingStyle',
            'FillingTransform',
            'FilterRules',
            'FinancialBond',
            'FinancialData',
            'FinancialDerivative',
            'FinancialIndicator',
            'Find',
            'FindArgMax',
            'FindArgMin',
            'FindChannels',
            'FindClique',
            'FindClusters',
            'FindCookies',
            'FindCurvePath',
            'FindCycle',
            'FindDevices',
            'FindDistribution',
            'FindDistributionParameters',
            'FindDivisions',
            'FindEdgeCover',
            'FindEdgeCut',
            'FindEdgeIndependentPaths',
            'FindEulerianCycle',
            'FindFaces',
            'FindFile',
            'FindFit',
            'FindFormula',
            'FindFundamentalCycles',
            'FindGeneratingFunction',
            'FindGeoLocation',
            'FindGeometricTransform',
            'FindGraphCommunities',
            'FindGraphIsomorphism',
            'FindGraphPartition',
            'FindHamiltonianCycle',
            'FindHamiltonianPath',
            'FindHiddenMarkovStates',
            'FindIndependentEdgeSet',
            'FindIndependentVertexSet',
            'FindInstance',
            'FindIntegerNullVector',
            'FindKClan',
            'FindKClique',
            'FindKClub',
            'FindKPlex',
            'FindLibrary',
            'FindLinearRecurrence',
            'FindList',
            'FindMaxValue',
            'FindMaximum',
            'FindMaximumFlow',
            'FindMeshDefects',
            'FindMinValue',
            'FindMinimum',
            'FindMinimumCostFlow',
            'FindMinimumCut',
            'FindPath',
            'FindPeaks',
            'FindPermutation',
            'FindPostmanTour',
            'FindProcessParameters',
            'FindRoot',
            'FindSequenceFunction',
            'FindSettings',
            'FindShortestPath',
            'FindShortestTour',
            'FindSpanningTree',
            'FindThreshold',
            'FindTransientRepeat',
            'FindVertexCover',
            'FindVertexCut',
            'FindVertexIndependentPaths',
            'FinishDynamic',
            'FiniteAbelianGroupCount',
            'FiniteGroupCount',
            'FiniteGroupData',
            'First',
            'FirstCase',
            'FirstPassageTimeDistribution',
            'FirstPosition',
            'FischerGroupFi22',
            'FischerGroupFi23',
            'FischerGroupFi24Prime',
            'FisherHypergeometricDistribution',
            'FisherRatioTest',
            'FisherZDistribution',
            'Fit',
            'FittedModel',
            'FixedOrder',
            'FixedPoint',
            'FixedPointList',
            'Flat',
            'FlatTopWindow',
            'Flatten',
            'FlattenAt',
            'FlattenLayer',
            'FlipView',
            'Floor',
            'FlowPolynomial',
            'Fold',
            'FoldList',
            'FoldPair',
            'FoldPairList',
            'FollowRedirects',
            'FontColor',
            'FontFamily',
            'FontSize',
            'FontSlant',
            'FontSubstitutions',
            'FontTracking',
            'FontVariations',
            'FontWeight',
            'For',
            'ForAll',
            'FormBox',
            'FormBoxOptions',
            'FormControl',
            'FormFunction',
            'FormLayoutFunction',
            'FormObject',
            'FormPage',
            'Format',
            'FormatType',
            'FormulaData',
            'FormulaLookup',
            'FortranForm',
            'Forward',
            'ForwardBackward',
            'Fourier',
            'FourierCoefficient',
            'FourierCosCoefficient',
            'FourierCosSeries',
            'FourierCosTransform',
            'FourierDCT',
            'FourierDCTFilter',
            'FourierDCTMatrix',
            'FourierDST',
            'FourierDSTMatrix',
            'FourierMatrix',
            'FourierParameters',
            'FourierSequenceTransform',
            'FourierSeries',
            'FourierSinCoefficient',
            'FourierSinSeries',
            'FourierSinTransform',
            'FourierTransform',
            'FourierTrigSeries',
            'FractionBox',
            'FractionBoxOptions',
            'FractionalBrownianMotionProcess',
            'FractionalGaussianNoiseProcess',
            'FractionalPart',
            'Frame',
            'FrameBox',
            'FrameBoxOptions',
            'FrameLabel',
            'FrameMargins',
            'FrameStyle',
            'FrameTicks',
            'FrameTicksStyle',
            'Framed',
            'FrechetDistribution',
            'FreeQ',
            'FrenetSerretSystem',
            'FrequencySamplingFilterKernel',
            'FresnelC',
            'FresnelF',
            'FresnelG',
            'FresnelS',
            'Friday',
            'FrobeniusNumber',
            'FrobeniusSolve',
            'FromCharacterCode',
            'FromCoefficientRules',
            'FromContinuedFraction',
            'FromDMS',
            'FromDigits',
            'FromEntity',
            'FromJulianDate',
            'FromLetterNumber',
            'FromPolarCoordinates',
            'FromRomanNumeral',
            'FromSphericalCoordinates',
            'FromUnixTime',
            'Front',
            'FrontEndDynamicExpression',
            'FrontEndEventActions',
            'FrontEndExecute',
            'FrontEndToken',
            'FrontEndTokenExecute',
            'Full',
            'FullDefinition',
            'FullForm',
            'FullGraphics',
            'FullInformationOutputRegulator',
            'FullRegion',
            'FullSimplify',
            'Function',
            'FunctionDomain',
            'FunctionExpand',
            'FunctionInterpolation',
            'FunctionPeriod',
            'FunctionRange',
            'FunctionSpace',
            'FussellVeselyImportance',

            'GARCHProcess',
            'GCD',
            'GaborFilter',
            'GaborMatrix',
            'GaborWavelet',
            'GainMargins',
            'GainPhaseMargins',
            'GalaxyData',
            'GalleryView',
            'Gamma',
            'GammaDistribution',
            'GammaRegularized',
            'GapPenalty',
            'Gather',
            'GatherBy',
            'GaugeFaceElementFunction',
            'GaugeFaceStyle',
            'GaugeFrameElementFunction',
            'GaugeFrameSize',
            'GaugeFrameStyle',
            'GaugeLabels',
            'GaugeMarkers',
            'GaugeStyle',
            'GaussianFilter',
            'GaussianIntegers',
            'GaussianMatrix',
            'GaussianOrthogonalMatrixDistribution',
            'GaussianSymplecticMatrixDistribution',
            'GaussianUnitaryMatrixDistribution',
            'GaussianWindow',
            'GegenbauerC',
            'General',
            'GeneralizedLinearModelFit',
            'GenerateAsymmetricKeyPair',
            'GenerateConditions',
            'GenerateDocument',
            'GenerateHTTPResponse',
            'GenerateSymmetricKey',
            'GeneratedCell',
            'GeneratedDocumentBinding',
            'GeneratedParameters',
            'GeneratingFunction',
            'GeneratorDescription',
            'GeneratorHistoryLength',
            'GeneratorOutputType',
            'GenericCylindricalDecomposition',
            'GenomeData',
            'GenomeLookup',
            'GeoArea',
            'GeoBackground',
            'GeoBoundingBox',
            'GeoBounds',
            'GeoBoundsRegion',
            'GeoCenter',
            'GeoCircle',
            'GeoDestination',
            'GeoDirection',
            'GeoDisk',
            'GeoDisplacement',
            'GeoDistance',
            'GeoDistanceList',
            'GeoElevationData',
            'GeoEntities',
            'GeoGraphics',
            'GeoGridLines',
            'GeoGridLinesStyle',
            'GeoGridPosition',
            'GeoGroup',
            'GeoHemisphere',
            'GeoHemisphereBoundary',
            'GeoHistogram',
            'GeoIdentify',
            'GeoLabels',
            'GeoLength',
            'GeoListPlot',
            'GeoLocation',
            'GeoMarker',
            'GeoModel',
            'GeoNearest',
            'GeoPath',
            'GeoPosition',
            'GeoPositionENU',
            'GeoPositionXYZ',
            'GeoProjection',
            'GeoProjectionData',
            'GeoRange',
            'GeoRangePadding',
            'GeoRegionValuePlot',
            'GeoScaleBar',
            'GeoServer',
            'GeoStyling',
            'GeoStylingImageFunction',
            'GeoVariant',
            'GeoVisibleRegion',
            'GeoVisibleRegionBoundary',
            'GeoWithinQ',
            'GeoZoomLevel',
            'GeodesicClosing',
            'GeodesicDilation',
            'GeodesicErosion',
            'GeodesicOpening',
            'GeodesyData',
            'GeogravityModelData',
            'GeologicalPeriodData',
            'GeomagneticModelData',
            'GeometricBrownianMotionProcess',
            'GeometricDistribution',
            'GeometricMean',
            'GeometricMeanFilter',
            'GeometricTransformation',
            'GestureHandler',
            'Get',
            'GetEnvironment',
            'Glaisher',
            'GlobalClusteringCoefficient',
            'Glow',
            'GoldenAngle',
            'GoldenRatio',
            'GompertzMakehamDistribution',
            'GoodmanKruskalGamma',
            'GoodmanKruskalGammaTest',
            'Goto',
            'Grad',
            'Gradient',
            'GradientFilter',
            'GradientOrientationFilter',
            'GrammarApply',
            'GrammarRules',
            'GrammarToken',
            'Graph',
            'Graph3D',
            'GraphAssortativity',
            'GraphAutomorphismGroup',
            'GraphCenter',
            'GraphComplement',
            'GraphData',
            'GraphDensity',
            'GraphDiameter',
            'GraphDifference',
            'GraphDisjointUnion',
            'GraphDistance',
            'GraphDistanceMatrix',
            'GraphEmbedding',
            'GraphHighlight',
            'GraphHighlightStyle',
            'GraphHub',
            'GraphIntersection',
            'GraphLayout',
            'GraphLinkEfficiency',
            'GraphPeriphery',
            'GraphPlot',
            'GraphPlot3D',
            'GraphPower',
            'GraphPropertyDistribution',
            'GraphQ',
            'GraphRadius',
            'GraphReciprocity',
            'GraphStyle',
            'GraphUnion',
            'Graphics',
            'Graphics3D',
            'GraphicsColumn',
            'GraphicsComplex',
            'GraphicsGrid',
            'GraphicsGroup',
            'GraphicsRow',
            'Gray',
            'GrayLevel',
            'Greater',
            'GreaterEqual',
            'GreaterEqualLess',
            'GreaterEqualThan',
            'GreaterFullEqual',
            'GreaterGreater',
            'GreaterLess',
            'GreaterSlantEqual',
            'GreaterThan',
            'GreaterTilde',
            'Green',
            'GreenFunction',
            'Grid',
            'GridBox',
            'GridDefaultElement',
            'GridGraph',
            'GridLines',
            'GridLinesStyle',
            'GroebnerBasis',
            'GroupActionBase',
            'GroupBy',
            'GroupCentralizer',
            'GroupElementFromWord',
            'GroupElementPosition',
            'GroupElementQ',
            'GroupElementToWord',
            'GroupElements',
            'GroupGenerators',
            'GroupMultiplicationTable',
            'GroupOrbits',
            'GroupOrder',
            'GroupPageBreakWithin',
            'GroupSetwiseStabilizer',
            'GroupStabilizer',
            'GroupStabilizerChain',
            'Groupings',
            'GrowCutComponents',
            'Gudermannian',
            'GuidedFilter',
            'GumbelDistribution',

            'HITSCentrality',
            'HTTPErrorResponse',
            'HTTPRedirect',
            'HTTPRequest',
            'HTTPRequestData',
            'HTTPResponse',
            'HaarWavelet',
            'HadamardMatrix',
            'HalfLine',
            'HalfNormalDistribution',
            'HalfPlane',
            'HalfSpace',
            'HamiltonianGraphQ',
            'HammingDistance',
            'HammingWindow',
            'HandlerFunctions',
            'HandlerFunctionsKeys',
            'HankelH1',
            'HankelH2',
            'HankelMatrix',
            'HannPoissonWindow',
            'HannWindow',
            'HaradaNortonGroupHN',
            'HararyGraph',
            'HarmonicMean',
            'HarmonicMeanFilter',
            'HarmonicNumber',
            'Hash',
            'Haversine',
            'HazardFunction',
            'Head',
            'HeaderLines',
            'Heads',
            'HeavisideLambda',
            'HeavisidePi',
            'HeavisideTheta',
            'HeldGroupHe',
            'Here',
            'HermiteDecomposition',
            'HermiteH',
            'HermitianMatrixQ',
            'HessenbergDecomposition',
            'HexadecimalCharacter',
            'Hexahedron',
            'HiddenMarkovProcess',
            'HighlightGraph',
            'HighlightImage',
            'HighlightMesh',
            'Highlighted',
            'HighpassFilter',
            'HigmanSimsGroupHS',
            'HilbertFilter',
            'HilbertMatrix',
            'Histogram',
            'Histogram3D',
            'HistogramDistribution',
            'HistogramList',
            'HistogramTransform',
            'HistogramTransformInterpolation',
            'HistoricalPeriodData',
            'HitMissTransform',
            'HodgeDual',
            'HoeffdingD',
            'HoeffdingDTest',
            'Hold',
            'HoldAll',
            'HoldAllComplete',
            'HoldComplete',
            'HoldFirst',
            'HoldForm',
            'HoldPattern',
            'HoldRest',
            'HolidayCalendar',
            'HorizontalGauge',
            'HornerForm',
            'HostLookup',
            'HotellingTSquareDistribution',
            'HoytDistribution',
            'Hue',
            'HumanGrowthData',
            'HumpDownHump',
            'HumpEqual',
            'HurwitzLerchPhi',
            'HurwitzZeta',
            'HyperbolicDistribution',
            'HypercubeGraph',
            'HyperexponentialDistribution',
            'Hyperfactorial',
            'Hypergeometric0F1',
            'Hypergeometric0F1Regularized',
            'Hypergeometric1F1',
            'Hypergeometric1F1Regularized',
            'Hypergeometric2F1',
            'Hypergeometric2F1Regularized',
            'HypergeometricDistribution',
            'HypergeometricPFQ',
            'HypergeometricPFQRegularized',
            'HypergeometricU',
            'Hyperlink',
            'Hyperplane',
            'Hyphenation',
            'HypoexponentialDistribution',
            'HypothesisTestData',

            'I',
            'IPAddress',
            'IconData',
            'IconRules',
            'Identity',
            'IdentityMatrix',
            'If',
            'IgnoreCase',
            'IgnoreDiacritics',
            'IgnorePunctuation',
            'IgnoringInactive',
            'Im',
            'Image',
            'Image3D',
            'Image3DSlices',
            'ImageAccumulate',
            'ImageAdd',
            'ImageAdjust',
            'ImageAlign',
            'ImageApply',
            'ImageApplyIndexed',
            'ImageAspectRatio',
            'ImageAssemble',
            'ImageCapture',
            'ImageChannels',
            'ImageClip',
            'ImageCollage',
            'ImageColorSpace',
            'ImageCompose',
            'ImageConvolve',
            'ImageCooccurrence',
            'ImageCorners',
            'ImageCorrelate',
            'ImageCorrespondingPoints',
            'ImageCrop',
            'ImageData',
            'ImageDeconvolve',
            'ImageDemosaic',
            'ImageDifference',
            'ImageDimensions',
            'ImageDisplacements',
            'ImageDistance',
            'ImageEffect',
            'ImageExposureCombine',
            'ImageFeatureTrack',
            'ImageFileApply',
            'ImageFileFilter',
            'ImageFileScan',
            'ImageFilter',
            'ImageFocusCombine',
            'ImageForestingComponents',
            'ImageFormattingWidth',
            'ImageForwardTransformation',
            'ImageHistogram',
            'ImageIdentify',
            'ImageInstanceQ',
            'ImageKeypoints',
            'ImageLevels',
            'ImageLines',
            'ImageMargins',
            'ImageMarker',
            'ImageMeasurements',
            'ImageMesh',
            'ImageMultiply',
            'ImagePad',
            'ImagePadding',
            'ImagePartition',
            'ImagePeriodogram',
            'ImagePerspectiveTransformation',
            'ImageQ',
            'ImageReflect',
            'ImageResize',
            'ImageResolution',
            'ImageRotate',
            'ImageSaliencyFilter',
            'ImageScaled',
            'ImageScan',
            'ImageSize',
            'ImageSizeAction',
            'ImageSizeMultipliers',
            'ImageSubtract',
            'ImageTake',
            'ImageTransformation',
            'ImageTrim',
            'ImageType',
            'ImageValue',
            'ImageValuePositions',
            'ImagingDevice',
            'ImplicitRegion',
            'Implies',
            'Import',
            'ImportOptions',
            'ImportString',
            'ImprovementImportance',
            'In',
            'InString',
            'Inactivate',
            'Inactive',
            'IncidenceGraph',
            'IncidenceList',
            'IncidenceMatrix',
            'IncludeConstantBasis',
            'IncludeGeneratorTasks',
            'IncludeInflections',
            'IncludeMetaInformation',
            'IncludePods',
            'IncludeQuantities',
            'IncludeWindowTimes',
            'Increment',
            'IndefiniteMatrixQ',
            'IndependenceTest',
            'IndependentEdgeSetQ',
            'IndependentUnit',
            'IndependentVertexSetQ',
            'Indeterminate',
            'IndeterminateThreshold',
            'IndexGraph',
            'Indexed',
            'InexactNumberQ',
            'InfiniteLine',
            'InfinitePlane',
            'Infinity',
            'Infix',
            'InflationAdjust',
            'InflationMethod',
            'Information',
            'InheritScope',
            'Inherited',
            'InhomogeneousPoissonProcess',
            'InitialEvaluationHistory',
            'Initialization',
            'InitializationCell',
            'Inner',
            'Inpaint',
            'Input',
            'InputAliases',
            'InputAssumptions',
            'InputAutoReplacements',
            'InputField',
            'InputForm',
            'InputNamePacket',
            'InputNotebook',
            'InputPacket',
            'InputStream',
            'InputString',
            'InputStringPacket',
            'Insert',
            'InsertLinebreaks',
            'InsertResults',
            'InsertionFunction',
            'Inset',
            'Insphere',
            'Install',
            'InstallService',
            'Integer',
            'IntegerDigits',
            'IntegerExponent',
            'IntegerLength',
            'IntegerName',
            'IntegerPart',
            'IntegerPartitions',
            'IntegerQ',
            'IntegerReverse',
            'IntegerString',
            'Integers',
            'Integrate',
            'Interactive',
            'InteractiveTradingChart',
            'Interleaving',
            'InternallyBalancedDecomposition',
            'InterpolatingFunction',
            'InterpolatingPolynomial',
            'Interpolation',
            'InterpolationOrder',
            'InterpolationPoints',
            'Interpretation',
            'InterpretationBox',
            'InterpretationBoxOptions',
            'Interpreter',
            'InterquartileRange',
            'Interrupt',
            'IntersectingQ',
            'Intersection',
            'Interval',
            'IntervalIntersection',
            'IntervalMemberQ',
            'IntervalSlider',
            'IntervalUnion',
            'Inverse',
            'InverseBetaRegularized',
            'InverseCDF',
            'InverseChiSquareDistribution',
            'InverseContinuousWaveletTransform',
            'InverseDistanceTransform',
            'InverseEllipticNomeQ',
            'InverseErf',
            'InverseErfc',
            'InverseFourier',
            'InverseFourierCosTransform',
            'InverseFourierSequenceTransform',
            'InverseFourierSinTransform',
            'InverseFourierTransform',
            'InverseFunction',
            'InverseFunctions',
            'InverseGammaDistribution',
            'InverseGammaRegularized',
            'InverseGaussianDistribution',
            'InverseGudermannian',
            'InverseHaversine',
            'InverseJacobiCD',
            'InverseJacobiCN',
            'InverseJacobiCS',
            'InverseJacobiDC',
            'InverseJacobiDN',
            'InverseJacobiDS',
            'InverseJacobiNC',
            'InverseJacobiND',
            'InverseJacobiNS',
            'InverseJacobiSC',
            'InverseJacobiSD',
            'InverseJacobiSN',
            'InverseLaplaceTransform',
            'InverseMellinTransform',
            'InversePermutation',
            'InverseRadon',
            'InverseSeries',
            'InverseSurvivalFunction',
            'InverseTransformedRegion',
            'InverseWaveletTransform',
            'InverseWeierstrassP',
            'InverseWishartMatrixDistribution',
            'InverseZTransform',
            'Invisible',
            'IrreduciblePolynomialQ',
            'IslandData',
            'IsolatingInterval',
            'IsomorphicGraphQ',
            'IsotopeData',
            'Italic',
            'Item',
            'ItemAspectRatio',
            'ItemSize',
            'ItemStyle',
            'ItoProcess',

            'JaccardDissimilarity',
            'JacobiAmplitude',
            'JacobiCD',
            'JacobiCN',
            'JacobiCS',
            'JacobiDC',
            'JacobiDN',
            'JacobiDS',
            'JacobiNC',
            'JacobiND',
            'JacobiNS',
            'JacobiP',
            'JacobiSC',
            'JacobiSD',
            'JacobiSN',
            'JacobiSymbol',
            'JacobiZeta',
            'JankoGroupJ1',
            'JankoGroupJ2',
            'JankoGroupJ3',
            'JankoGroupJ4',
            'JarqueBeraALMTest',
            'JohnsonDistribution',
            'Join',
            'JoinAcross',
            'JoinForm',
            'Joined',
            'JoinedCurve',
            'JordanDecomposition',
            'JordanModelDecomposition',
            'JuliaSetBoettcher',
            'JuliaSetIterationCount',
            'JuliaSetPlot',
            'JuliaSetPoints',
            'JulianDate',

            'KCoreComponents',
            'KDistribution',
            'KEdgeConnectedComponents',
            'KEdgeConnectedGraphQ',
            'KVertexConnectedComponents',
            'KVertexConnectedGraphQ',
            'KagiChart',
            'KaiserBesselWindow',
            'KaiserWindow',
            'KalmanEstimator',
            'KalmanFilter',
            'KarhunenLoeveDecomposition',
            'KaryTree',
            'KatzCentrality',
            'KelvinBei',
            'KelvinBer',
            'KelvinKei',
            'KelvinKer',
            'KendallTau',
            'KendallTauTest',
            'KernelMixtureDistribution',
            'KernelObject',
            'Kernels',
            'Key',
            'KeyCollisionFunction',
            'KeyComplement',
            'KeyDrop',
            'KeyDropFrom',
            'KeyExistsQ',
            'KeyFreeQ',
            'KeyIntersection',
            'KeyMap',
            'KeyMemberQ',
            'KeySelect',
            'KeySort',
            'KeySortBy',
            'KeyTake',
            'KeyUnion',
            'KeyValueMap',
            'KeyValuePattern',
            'KeypointStrength',
            'Keys',
            'Khinchin',
            'KillProcess',
            'KirchhoffGraph',
            'KirchhoffMatrix',
            'KleinInvariantJ',
            'KnapsackSolve',
            'KnightTourGraph',
            'KnotData',
            'KnownUnitQ',
            'KolmogorovSmirnovTest',
            'KroneckerDelta',
            'KroneckerModelDecomposition',
            'KroneckerProduct',
            'KroneckerSymbol',
            'KuiperTest',
            'KumaraswamyDistribution',
            'Kurtosis',
            'KuwaharaFilter',

            'LABColor',
            'LCHColor',
            'LCM',
            'LQEstimatorGains',
            'LQGRegulator',
            'LQOutputRegulatorGains',
            'LQRegulatorGains',
            'LUDecomposition',
            'LUVColor',
            'Label',
            'LabelStyle',
            'Labeled',
            'LabelingFunction',
            'LaguerreL',
            'LakeData',
            'LambdaComponents',
            'LaminaData',
            'LanczosWindow',
            'LandauDistribution',
            'Language',
            'LanguageCategory',
            'LanguageData',
            'LanguageIdentify',
            'LaplaceDistribution',
            'LaplaceTransform',
            'Laplacian',
            'LaplacianFilter',
            'LaplacianGaussianFilter',
            'Large',
            'Larger',
            'Last',
            'Latitude',
            'LatitudeLongitude',
            'LatticeData',
            'LatticeReduce',
            'LaunchKernels',
            'LayerSizeFunction',
            'LayeredGraphPlot',
            'LeaderSize',
            'LeafCount',
            'LeapYearQ',
            'LeastSquares',
            'LeastSquaresFilterKernel',
            'Left',
            'LeftArrow',
            'LeftArrowBar',
            'LeftArrowRightArrow',
            'LeftDownTeeVector',
            'LeftDownVector',
            'LeftDownVectorBar',
            'LeftRightArrow',
            'LeftRightVector',
            'LeftTee',
            'LeftTeeArrow',
            'LeftTeeVector',
            'LeftTriangle',
            'LeftTriangleBar',
            'LeftTriangleEqual',
            'LeftUpDownVector',
            'LeftUpTeeVector',
            'LeftUpVector',
            'LeftUpVectorBar',
            'LeftVector',
            'LeftVectorBar',
            'LegendAppearance',
            'LegendFunction',
            'LegendLabel',
            'LegendLayout',
            'LegendMargins',
            'LegendMarkerSize',
            'LegendMarkers',
            'Legended',
            'LegendreP',
            'LegendreQ',
            'Length',
            'LengthWhile',
            'LerchPhi',
            'Less',
            'LessEqual',
            'LessEqualGreater',
            'LessEqualThan',
            'LessFullEqual',
            'LessGreater',
            'LessLess',
            'LessSlantEqual',
            'LessThan',
            'LessTilde',
            'LetterCharacter',
            'LetterCounts',
            'LetterNumber',
            'LetterQ',
            'Level',
            'LeveneTest',
            'LeviCivitaTensor',
            'LevyDistribution',
            'LibraryDataType',
            'LibraryFunction',
            'LibraryFunctionError',
            'LibraryFunctionInformation',
            'LibraryFunctionLoad',
            'LibraryFunctionUnload',
            'LibraryLoad',
            'LibraryUnload',
            'LiftingFilterData',
            'LiftingWaveletTransform',
            'LightBlue',
            'LightBrown',
            'LightCyan',
            'LightGray',
            'LightGreen',
            'LightMagenta',
            'LightOrange',
            'LightPink',
            'LightPurple',
            'LightRed',
            'LightYellow',
            'Lighter',
            'Lighting',
            'LightingAngle',
            'Likelihood',
            'Limit',
            'LimitsPositioning',
            'LindleyDistribution',
            'Line',
            'LineBreakChart',
            'LineGraph',
            'LineIndent',
            'LineIndentMaxFraction',
            'LineIntegralConvolutionPlot',
            'LineIntegralConvolutionScale',
            'LineLegend',
            'LineSpacing',
            'LinearFractionalTransform',
            'LinearGradientImage',
            'LinearModelFit',
            'LinearOffsetFunction',
            'LinearProgramming',
            'LinearRecurrence',
            'LinearSolve',
            'LinearSolveFunction',
            'LinearizingTransformationData',
            'LinkActivate',
            'LinkClose',
            'LinkConnect',
            'LinkCreate',
            'LinkFunction',
            'LinkInterrupt',
            'LinkLaunch',
            'LinkObject',
            'LinkPatterns',
            'LinkProtocol',
            'LinkRankCentrality',
            'LinkRead',
            'LinkReadyQ',
            'LinkWrite',
            'Links',
            'LiouvilleLambda',
            'List',
            'ListAnimate',
            'ListContourPlot',
            'ListContourPlot3D',
            'ListConvolve',
            'ListCorrelate',
            'ListCurvePathPlot',
            'ListDeconvolve',
            'ListDensityPlot',
            'ListDensityPlot3D',
            'ListFormat',
            'ListFourierSequenceTransform',
            'ListInterpolation',
            'ListLineIntegralConvolutionPlot',
            'ListLinePlot',
            'ListLogLinearPlot',
            'ListLogLogPlot',
            'ListLogPlot',
            'ListPicker',
            'ListPickerBox',
            'ListPickerBoxOptions',
            'ListPlay',
            'ListPlot',
            'ListPlot3D',
            'ListPointPlot3D',
            'ListPolarPlot',
            'ListQ',
            'ListSliceContourPlot3D',
            'ListSliceDensityPlot3D',
            'ListSliceVectorPlot3D',
            'ListStepPlot',
            'ListStreamDensityPlot',
            'ListStreamPlot',
            'ListSurfacePlot3D',
            'ListVectorDensityPlot',
            'ListVectorPlot',
            'ListVectorPlot3D',
            'ListZTransform',
            'Listable',
            'LocalAdaptiveBinarize',
            'LocalCache',
            'LocalClusteringCoefficient',
            'LocalObject',
            'LocalObjects',
            'LocalSymbol',
            'LocalTime',
            'LocalTimeZone',
            'LocalizeVariables',
            'LocationEquivalenceTest',
            'LocationTest',
            'Locator',
            'LocatorAutoCreate',
            'LocatorPane',
            'LocatorRegion',
            'Locked',
            'Log',
            'Log10',
            'Log2',
            'LogBarnesG',
            'LogGamma',
            'LogGammaDistribution',
            'LogIntegral',
            'LogLikelihood',
            'LogLinearPlot',
            'LogLogPlot',
            'LogLogisticDistribution',
            'LogMultinormalDistribution',
            'LogNormalDistribution',
            'LogPlot',
            'LogRankTest',
            'LogSeriesDistribution',
            'LogicalExpand',
            'LogisticDistribution',
            'LogisticSigmoid',
            'LogitModelFit',
            'LongLeftArrow',
            'LongLeftRightArrow',
            'LongRightArrow',
            'Longest',
            'LongestCommonSequence',
            'LongestCommonSequencePositions',
            'LongestCommonSubsequence',
            'LongestCommonSubsequencePositions',
            'LongestOrderedSequence',
            'Longitude',
            'Lookup',
            'LoopFreeGraphQ',
            'LowerCaseQ',
            'LowerLeftArrow',
            'LowerRightArrow',
            'LowerTriangularize',
            'LowpassFilter',
            'LucasL',
            'LuccioSamiComponents',
            'LunarEclipse',
            'LyapunovSolve',
            'LyonsGroupLy',

            'MAProcess',
            'MachineNumberQ',
            'MachinePrecision',
            'Magenta',
            'Magnification',
            'Magnify',
            'MailReceiverFunction',
            'MailResponseFunction',
            'Majority',
            'MakeBoxes',
            'MakeExpression',
            'ManagedLibraryExpressionID',
            'ManagedLibraryExpressionQ',
            'MandelbrotSetBoettcher',
            'MandelbrotSetDistance',
            'MandelbrotSetIterationCount',
            'MandelbrotSetMemberQ',
            'MandelbrotSetPlot',
            'MangoldtLambda',
            'ManhattanDistance',
            'Manipulate',
            'Manipulator',
            'MannWhitneyTest',
            'MannedSpaceMissionData',
            'MantissaExponent',
            'Manual',
            'Map',
            'MapAll',
            'MapAt',
            'MapIndexed',
            'MapThread',
            'MarchenkoPasturDistribution',
            'MarcumQ',
            'MardiaCombinedTest',
            'MardiaKurtosisTest',
            'MardiaSkewnessTest',
            'MarginalDistribution',
            'MarkovProcessProperties',
            'Masking',
            'MatchLocalNames',
            'MatchQ',
            'MatchingDissimilarity',
            'MathMLForm',
            'MathematicalFunctionData',
            'MathieuC',
            'MathieuCPrime',
            'MathieuCharacteristicA',
            'MathieuCharacteristicB',
            'MathieuCharacteristicExponent',
            'MathieuGroupM11',
            'MathieuGroupM12',
            'MathieuGroupM22',
            'MathieuGroupM23',
            'MathieuGroupM24',
            'MathieuS',
            'MathieuSPrime',
            'Matrices',
            'MatrixExp',
            'MatrixForm',
            'MatrixFunction',
            'MatrixLog',
            'MatrixNormalDistribution',
            'MatrixPlot',
            'MatrixPower',
            'MatrixPropertyDistribution',
            'MatrixQ',
            'MatrixRank',
            'MatrixTDistribution',
            'Max',
            'MaxCellMeasure',
            'MaxDetect',
            'MaxExtraBandwidths',
            'MaxExtraConditions',
            'MaxFeatureDisplacement',
            'MaxFeatures',
            'MaxFilter',
            'MaxItems',
            'MaxIterations',
            'MaxMemoryUsed',
            'MaxMixtureKernels',
            'MaxPlotPoints',
            'MaxRecursion',
            'MaxStableDistribution',
            'MaxStepFraction',
            'MaxStepSize',
            'MaxSteps',
            'MaxTrainingRounds',
            'MaxValue',
            'MaxWordGap',
            'MaximalBy',
            'Maximize',
            'MaxwellDistribution',
            'McLaughlinGroupMcL',
            'Mean',
            'MeanAbsoluteLossLayer',
            'MeanClusteringCoefficient',
            'MeanDegreeConnectivity',
            'MeanDeviation',
            'MeanFilter',
            'MeanGraphDistance',
            'MeanNeighborDegree',
            'MeanShift',
            'MeanShiftFilter',
            'MeanSquaredLossLayer',
            'Median',
            'MedianDeviation',
            'MedianFilter',
            'MedicalTestData',
            'Medium',
            'MeijerG',
            'MeijerGReduce',
            'MeixnerDistribution',
            'MellinConvolve',
            'MellinTransform',
            'MemberQ',
            'MemoryConstrained',
            'MemoryConstraint',
            'MemoryInUse',
            'MenuCommandKey',
            'MenuPacket',
            'MenuSortingValue',
            'MenuStyle',
            'MenuView',
            'Merge',
            'MersennePrimeExponent',
            'MersennePrimeExponentQ',
            'Mesh',
            'MeshCellCentroid',
            'MeshCellCount',
            'MeshCellHighlight',
            'MeshCellIndex',
            'MeshCellLabel',
            'MeshCellMarker',
            'MeshCellMeasure',
            'MeshCellQuality',
            'MeshCellShapeFunction',
            'MeshCellStyle',
            'MeshCells',
            'MeshCoordinates',
            'MeshFunctions',
            'MeshPrimitives',
            'MeshQualityGoal',
            'MeshRefinementFunction',
            'MeshRegion',
            'MeshRegionQ',
            'MeshShading',
            'MeshStyle',
            'Message',
            'MessageDialog',
            'MessageList',
            'MessageName',
            'MessagePacket',
            'Messages',
            'MetaInformation',
            'MeteorShowerData',
            'Method',
            'MexicanHatWavelet',
            'MeyerWavelet',
            'Min',
            'MinColorDistance',
            'MinDetect',
            'MinFilter',
            'MinIntervalSize',
            'MinMax',
            'MinStableDistribution',
            'MinValue',
            'MineralData',
            'MinimalBy',
            'MinimalPolynomial',
            'MinimalStateSpaceModel',
            'Minimize',
            'MinimumTimeIncrement',
            'MinkowskiQuestionMark',
            'MinorPlanetData',
            'Minors',
            'Minus',
            'MinusPlus',
            'Missing',
            'MissingBehavior',
            'MissingDataMethod',
            'MissingDataRules',
            'MissingQ',
            'MissingString',
            'MissingStyle',
            'MittagLefflerE',
            'MixedGraphQ',
            'MixedMagnitude',
            'MixedRadix',
            'MixedRadixQuantity',
            'MixedUnit',
            'MixtureDistribution',
            'Mod',
            'Modal',
            'ModularLambda',
            'Module',
            'Modulus',
            'MoebiusMu',
            'Moment',
            'MomentConvert',
            'MomentEvaluate',
            'MomentGeneratingFunction',
            'MomentOfInertia',
            'Monday',
            'Monitor',
            'MonomialList',
            'MonsterGroupM',
            'MoonPhase',
            'MoonPosition',
            'MorletWavelet',
            'MorphologicalBinarize',
            'MorphologicalBranchPoints',
            'MorphologicalComponents',
            'MorphologicalEulerNumber',
            'MorphologicalGraph',
            'MorphologicalPerimeter',
            'MorphologicalTransform',
            'MortalityData',
            'Most',
            'MountainData',
            'MouseAnnotation',
            'MouseAppearance',
            'MousePosition',
            'Mouseover',
            'MovieData',
            'MovingAverage',
            'MovingMap',
            'MovingMedian',
            'MoyalDistribution',
            'Multicolumn',
            'MultiedgeStyle',
            'MultigraphQ',
            'Multinomial',
            'MultinomialDistribution',
            'MultinormalDistribution',
            'MultiplicativeOrder',
            'Multiselection',
            'MultivariateHypergeometricDistribution',
            'MultivariatePoissonDistribution',
            'MultivariateTDistribution',

            'N',
            'NArgMax',
            'NArgMin',
            'NCache',
            'NDEigensystem',
            'NDEigenvalues',
            'NDSolve',
            'NDSolveValue',
            'NExpectation',
            'NHoldAll',
            'NHoldFirst',
            'NHoldRest',
            'NIntegrate',
            'NMaxValue',
            'NMaximize',
            'NMinValue',
            'NMinimize',
            'NProbability',
            'NProduct',
            'NRoots',
            'NSolve',
            'NSum',
            'NakagamiDistribution',
            'NameQ',
            'Names',
            'Nand',
            'Nearest',
            'NearestFunction',
            'NearestNeighborGraph',
            'NebulaData',
            'NeedlemanWunschSimilarity',
            'Needs',
            'Negative',
            'NegativeBinomialDistribution',
            'NegativeDefiniteMatrixQ',
            'NegativeMultinomialDistribution',
            'NegativeSemidefiniteMatrixQ',
            'NeighborhoodData',
            'NeighborhoodGraph',
            'Nest',
            'NestGraph',
            'NestList',
            'NestWhile',
            'NestWhileList',
            'NestedGreaterGreater',
            'NestedLessLess',
            'NetChain',
            'NetDecoder',
            'NetEncoder',
            'NetExtract',
            'NetGraph',
            'NetInitialize',
            'NetPort',
            'NetTrain',
            'NeumannValue',
            'NevilleThetaC',
            'NevilleThetaD',
            'NevilleThetaN',
            'NevilleThetaS',
            'NextCell',
            'NextPrime',
            'NextScheduledTaskTime',
            'NicholsGridLines',
            'NicholsPlot',
            'NightHemisphere',
            'NoWhitespace',
            'NominalVariables',
            'NonCommutativeMultiply',
            'NonConstants',
            'NonNegative',
            'NonPositive',
            'NoncentralBetaDistribution',
            'NoncentralChiSquareDistribution',
            'NoncentralFRatioDistribution',
            'NoncentralStudentTDistribution',
            'None',
            'NoneTrue',
            'NonlinearModelFit',
            'NonlinearStateSpaceModel',
            'NonlocalMeansFilter',
            'Nor',
            'NorlundB',
            'Norm',
            'NormFunction',
            'Normal',
            'NormalDistribution',
            'NormalMatrixQ',
            'Normalize',
            'Normalized',
            'NormalizedSquaredEuclideanDistance',
            'NormalsFunction',
            'Not',
            'NotCongruent',
            'NotCupCap',
            'NotDoubleVerticalBar',
            'NotElement',
            'NotEqualTilde',
            'NotExists',
            'NotGreater',
            'NotGreaterEqual',
            'NotGreaterFullEqual',
            'NotGreaterGreater',
            'NotGreaterLess',
            'NotGreaterSlantEqual',
            'NotGreaterTilde',
            'NotHumpDownHump',
            'NotHumpEqual',
            'NotLeftTriangle',
            'NotLeftTriangleBar',
            'NotLeftTriangleEqual',
            'NotLess',
            'NotLessEqual',
            'NotLessFullEqual',
            'NotLessGreater',
            'NotLessLess',
            'NotLessSlantEqual',
            'NotLessTilde',
            'NotNestedGreaterGreater',
            'NotNestedLessLess',
            'NotPrecedes',
            'NotPrecedesEqual',
            'NotPrecedesSlantEqual',
            'NotPrecedesTilde',
            'NotReverseElement',
            'NotRightTriangle',
            'NotRightTriangleBar',
            'NotRightTriangleEqual',
            'NotSquareSubset',
            'NotSquareSubsetEqual',
            'NotSquareSuperset',
            'NotSquareSupersetEqual',
            'NotSubset',
            'NotSubsetEqual',
            'NotSucceeds',
            'NotSucceedsEqual',
            'NotSucceedsSlantEqual',
            'NotSucceedsTilde',
            'NotSuperset',
            'NotSupersetEqual',
            'NotTilde',
            'NotTildeEqual',
            'NotTildeFullEqual',
            'NotTildeTilde',
            'NotVerticalBar',
            'Notebook',
            'NotebookApply',
            'NotebookAutoSave',
            'NotebookClose',
            'NotebookDelete',
            'NotebookDirectory',
            'NotebookDynamicExpression',
            'NotebookEvaluate',
            'NotebookEventActions',
            'NotebookFileName',
            'NotebookFind',
            'NotebookGet',
            'NotebookImport',
            'NotebookInformation',
            'NotebookLocate',
            'NotebookObject',
            'NotebookOpen',
            'NotebookPrint',
            'NotebookPut',
            'NotebookRead',
            'NotebookSave',
            'NotebookSelection',
            'NotebookTemplate',
            'NotebookWrite',
            'Notebooks',
            'Nothing',
            'NotificationFunction',
            'Now',
            'NuclearExplosionData',
            'NuclearReactorData',
            'Null',
            'NullRecords',
            'NullSpace',
            'NullWords',
            'Number',
            'NumberCompose',
            'NumberDecompose',
            'NumberExpand',
            'NumberFieldClassNumber',
            'NumberFieldDiscriminant',
            'NumberFieldFundamentalUnits',
            'NumberFieldIntegralBasis',
            'NumberFieldNormRepresentatives',
            'NumberFieldRegulator',
            'NumberFieldRootsOfUnity',
            'NumberFieldSignature',
            'NumberForm',
            'NumberFormat',
            'NumberLinePlot',
            'NumberMarks',
            'NumberMultiplier',
            'NumberPadding',
            'NumberPoint',
            'NumberQ',
            'NumberSeparator',
            'NumberSigns',
            'NumberString',
            'Numerator',
            'NumericFunction',
            'NumericQ',
            'NuttallWindow',
            'NyquistGridLines',
            'NyquistPlot',

            'O',
            'ONanGroupON',
            'ObservabilityGramian',
            'ObservabilityMatrix',
            'ObservableDecomposition',
            'ObservableModelQ',
            'OceanData',
            'OddQ',
            'Off',
            'Offset',
            'On',
            'Once',
            'OneIdentity',
            'Opacity',
            'OpacityFunction',
            'OpacityFunctionScaling',
            'OpenAppend',
            'OpenRead',
            'OpenWrite',
            'Opener',
            'OpenerView',
            'Opening',
            'Operate',
            'OperatingSystem',
            'OptimumFlowData',
            'OptionValue',
            'Optional',
            'OptionalElement',
            'Options',
            'OptionsPattern',
            'Or',
            'Orange',
            'Order',
            'OrderDistribution',
            'OrderedQ',
            'Ordering',
            'Orderless',
            'OrderlessPatternSequence',
            'OrnsteinUhlenbeckProcess',
            'OrthogonalMatrixQ',
            'Orthogonalize',
            'Out',
            'Outer',
            'OutputControllabilityMatrix',
            'OutputControllableModelQ',
            'OutputForm',
            'OutputNamePacket',
            'OutputResponse',
            'OutputSizeLimit',
            'OutputStream',
            'OverBar',
            'OverDot',
            'OverHat',
            'OverTilde',
            'OverVector',
            'Overflow',
            'Overlaps',
            'Overlay',
            'Overscript',
            'OverscriptBox',
            'OverscriptBoxOptions',
            'OverwriteTarget',
            'OwenT',
            'OwnValues',

            'PDF',
            'PERTDistribution',
            'PIDData',
            'PIDDerivativeFilter',
            'PIDFeedforward',
            'PIDTune',
            'PackingMethod',
            'PadLeft',
            'PadRight',
            'PaddedForm',
            'Padding',
            'PaddingSize',
            'PadeApproximant',
            'PageBreakAbove',
            'PageBreakBelow',
            'PageBreakWithin',
            'PageFooters',
            'PageHeaders',
            'PageRankCentrality',
            'PageTheme',
            'PageWidth',
            'Pagination',
            'PairedBarChart',
            'PairedHistogram',
            'PairedSmoothHistogram',
            'PairedTTest',
            'PairedZTest',
            'PaletteNotebook',
            'PalindromeQ',
            'Pane',
            'PaneSelector',
            'Panel',
            'Paneled',
            'ParabolicCylinderD',
            'ParagraphIndent',
            'ParagraphSpacing',
            'ParallelArray',
            'ParallelCombine',
            'ParallelDo',
            'ParallelEvaluate',
            'ParallelMap',
            'ParallelNeeds',
            'ParallelProduct',
            'ParallelSubmit',
            'ParallelSum',
            'ParallelTable',
            'ParallelTry',
            'Parallelepiped',
            'Parallelization',
            'Parallelize',
            'Parallelogram',
            'ParameterEstimator',
            'ParameterMixtureDistribution',
            'ParametricFunction',
            'ParametricNDSolve',
            'ParametricNDSolveValue',
            'ParametricPlot',
            'ParametricPlot3D',
            'ParametricRegion',
            'ParentBox',
            'ParentCell',
            'ParentDirectory',
            'ParentNotebook',
            'ParetoDistribution',
            'ParkData',
            'Part',
            'PartBehavior',
            'PartOfSpeech',
            'PartProtection',
            'PartialCorrelationFunction',
            'ParticleAcceleratorData',
            'ParticleData',
            'Partition',
            'PartitionGranularity',
            'PartitionsP',
            'PartitionsQ',
            'ParzenWindow',
            'PascalDistribution',
            'PassEventsDown',
            'PassEventsUp',
            'Paste',
            'PasteButton',
            'Path',
            'PathGraph',
            'PathGraphQ',
            'Pattern',
            'PatternSequence',
            'PatternTest',
            'PaulWavelet',
            'PauliMatrix',
            'Pause',
            'PeakDetect',
            'PearsonChiSquareTest',
            'PearsonCorrelationTest',
            'PearsonDistribution',
            'PerfectNumber',
            'PerfectNumberQ',
            'PerformanceGoal',
            'PeriodicBoundaryCondition',
            'Periodogram',
            'PeriodogramArray',
            'Permanent',
            'Permissions',
            'PermissionsGroup',
            'PermissionsGroups',
            'PermissionsKey',
            'PermissionsKeys',
            'PermutationCycles',
            'PermutationCyclesQ',
            'PermutationGroup',
            'PermutationLength',
            'PermutationList',
            'PermutationListQ',
            'PermutationMax',
            'PermutationMin',
            'PermutationOrder',
            'PermutationPower',
            'PermutationProduct',
            'PermutationReplace',
            'PermutationSupport',
            'Permutations',
            'Permute',
            'PeronaMalikFilter',
            'PersonData',
            'PetersenGraph',
            'PhaseMargins',
            'PhaseRange',
            'PhysicalSystemData',
            'Pi',
            'Pick',
            'PieChart',
            'PieChart3D',
            'Piecewise',
            'PiecewiseExpand',
            'PillaiTrace',
            'PillaiTraceTest',
            'PingTime',
            'Pink',
            'PixelConstrained',
            'PixelValue',
            'PixelValuePositions',
            'Placed',
            'Placeholder',
            'PlaceholderReplace',
            'Plain',
            'PlanarGraph',
            'PlanarGraphQ',
            'PlanckRadiationLaw',
            'PlaneCurveData',
            'PlanetData',
            'PlanetaryMoonData',
            'PlantData',
            'Play',
            'PlayRange',
            'Plot',
            'Plot3D',
            'PlotLabel',
            'PlotLabels',
            'PlotLayout',
            'PlotLegends',
            'PlotMarkers',
            'PlotPoints',
            'PlotRange',
            'PlotRangeClipping',
            'PlotRangePadding',
            'PlotRegion',
            'PlotStyle',
            'PlotTheme',
            'Pluralize',
            'Plus',
            'PlusMinus',
            'Pochhammer',
            'PodStates',
            'PodWidth',
            'Point',
            'PointFigureChart',
            'PointLegend',
            'PointSize',
            'PoissonConsulDistribution',
            'PoissonDistribution',
            'PoissonProcess',
            'PoissonWindow',
            'PolarAxes',
            'PolarAxesOrigin',
            'PolarGridLines',
            'PolarPlot',
            'PolarTicks',
            'PoleZeroMarkers',
            'PolyGamma',
            'PolyLog',
            'PolyaAeppliDistribution',
            'Polygon',
            'PolygonalNumber',
            'PolyhedronData',
            'PolynomialExtendedGCD',
            'PolynomialGCD',
            'PolynomialLCM',
            'PolynomialMod',
            'PolynomialQ',
            'PolynomialQuotient',
            'PolynomialQuotientRemainder',
            'PolynomialReduce',
            'PolynomialRemainder',
            'PoolingLayer',
            'PopupMenu',
            'PopupView',
            'PopupWindow',
            'Position',
            'PositionIndex',
            'Positive',
            'PositiveDefiniteMatrixQ',
            'PositiveSemidefiniteMatrixQ',
            'PossibleZeroQ',
            'Postfix',
            'Power',
            'PowerDistribution',
            'PowerExpand',
            'PowerMod',
            'PowerModList',
            'PowerRange',
            'PowerSpectralDensity',
            'PowerSymmetricPolynomial',
            'PowersRepresentations',
            'PreDecrement',
            'PreIncrement',
            'PrecedenceForm',
            'Precedes',
            'PrecedesEqual',
            'PrecedesSlantEqual',
            'PrecedesTilde',
            'Precision',
            'PrecisionGoal',
            'Predict',
            'PredictorFunction',
            'PredictorInformation',
            'PredictorMeasurements',
            'PredictorMeasurementsObject',
            'PreemptProtect',
            'Prefix',
            'Prepend',
            'PrependTo',
            'PreserveImageOptions',
            'PreviousCell',
            'PriceGraphDistribution',
            'Prime',
            'PrimeNu',
            'PrimeOmega',
            'PrimePi',
            'PrimePowerQ',
            'PrimeQ',
            'PrimeZetaP',
            'Primes',
            'PrimitiveRoot',
            'PrimitiveRootList',
            'PrincipalComponents',
            'PrincipalValue',
            'Print',
            'PrintTemporary',
            'PrintableASCIIQ',
            'PrintingStyleEnvironment',
            'Printout3D',
            'Printout3DPreviewer',
            'Prism',
            'PrivateCellOptions',
            'PrivateFontOptions',
            'PrivateKey',
            'PrivateNotebookOptions',
            'Probability',
            'ProbabilityDistribution',
            'ProbabilityPlot',
            'ProbabilityScalePlot',
            'ProbitModelFit',
            'ProcessConnection',
            'ProcessDirectory',
            'ProcessEnvironment',
            'ProcessEstimator',
            'ProcessInformation',
            'ProcessObject',
            'ProcessParameterAssumptions',
            'ProcessParameterQ',
            'ProcessStatus',
            'Processes',
            'Product',
            'ProductDistribution',
            'ProductLog',
            'ProgressIndicator',
            'Projection',
            'Prolog',
            'Properties',
            'Property',
            'PropertyList',
            'PropertyValue',
            'Proportion',
            'Proportional',
            'Protect',
            'Protected',
            'ProteinData',
            'Pruning',
            'PseudoInverse',
            'PublicKey',
            'PulsarData',
            'PunctuationCharacter',
            'Purple',
            'Put',
            'PutAppend',
            'Pyramid',

            'QBinomial',
            'QFactorial',
            'QGamma',
            'QHypergeometricPFQ',
            'QPochhammer',
            'QPolyGamma',
            'QRDecomposition',
            'QuadraticIrrationalQ',
            'Quantile',
            'QuantilePlot',
            'Quantity',
            'QuantityArray',
            'QuantityDistribution',
            'QuantityForm',
            'QuantityMagnitude',
            'QuantityQ',
            'QuantityUnit',
            'QuantityVariable',
            'QuantityVariableCanonicalUnit',
            'QuantityVariableDimensions',
            'QuantityVariableIdentifier',
            'QuantityVariablePhysicalQuantity',
            'Quartics',
            'QuartileDeviation',
            'QuartileSkewness',
            'Quartiles',
            'Query',
            'QueueProperties',
            'QueueingNetworkProcess',
            'QueueingProcess',
            'Quiet',
            'Quit',
            'Quotient',
            'QuotientRemainder',

            'RGBColor',
            'RSolve',
            'RSolveValue',
            'RadialGradientImage',
            'RadialityCentrality',
            'RadicalBox',
            'RadicalBoxOptions',
            'RadioButton',
            'RadioButtonBar',
            'Radon',
            'RamanujanTau',
            'RamanujanTauL',
            'RamanujanTauTheta',
            'RamanujanTauZ',
            'Ramp',
            'RandomChoice',
            'RandomColor',
            'RandomComplex',
            'RandomEntity',
            'RandomFunction',
            'RandomGraph',
            'RandomImage',
            'RandomInteger',
            'RandomPermutation',
            'RandomPoint',
            'RandomPrime',
            'RandomReal',
            'RandomSample',
            'RandomVariate',
            'RandomWalkProcess',
            'RandomWord',
            'Range',
            'RangeFilter',
            'RankedMax',
            'RankedMin',
            'Raster',
            'Raster3D',
            'RasterSize',
            'Rasterize',
            'Rational',
            'Rationalize',
            'Rationals',
            'Ratios',
            'RawBoxes',
            'RawData',
            'RayleighDistribution',
            'Re',
            'ReIm',
            'Read',
            'ReadLine',
            'ReadList',
            'ReadProtected',
            'ReadString',
            'Real',
            'RealBlockDiagonalForm',
            'RealDigits',
            'RealExponent',
            'Reals',
            'Reap',
            'RecognitionPrior',
            'RecognitionThreshold',
            'Record',
            'RecordLists',
            'RecordSeparators',
            'Rectangle',
            'RectangleChart',
            'RectangleChart3D',
            'RecurrenceFilter',
            'RecurrenceTable',
            'Red',
            'Reduce',
            'ReferenceLineStyle',
            'Refine',
            'ReflectionMatrix',
            'ReflectionTransform',
            'Refresh',
            'RefreshRate',
            'RegionBinarize',
            'RegionBoundary',
            'RegionBounds',
            'RegionCentroid',
            'RegionDifference',
            'RegionDimension',
            'RegionDistance',
            'RegionDistanceFunction',
            'RegionEmbeddingDimension',
            'RegionFunction',
            'RegionIntersection',
            'RegionMeasure',
            'RegionMember',
            'RegionMemberFunction',
            'RegionMoment',
            'RegionNearest',
            'RegionNearestFunction',
            'RegionPlot',
            'RegionPlot3D',
            'RegionProduct',
            'RegionQ',
            'RegionResize',
            'RegionSize',
            'RegionSymmetricDifference',
            'RegionUnion',
            'RegularExpression',
            'RegularPolygon',
            'Regularization',
            'RegularlySampledQ',
            'RelationGraph',
            'ReleaseHold',
            'ReliabilityDistribution',
            'ReliefImage',
            'ReliefPlot',
            'Remove',
            'RemoveAlphaChannel',
            'RemoveAsynchronousTask',
            'RemoveBackground',
            'RemoveChannelListener',
            'RemoveDiacritics',
            'RemoveInputStreamMethod',
            'RemoveOutputStreamMethod',
            'RemoveProperty',
            'RemoveScheduledTask',
            'RemoveUsers',
            'RenameDirectory',
            'RenameFile',
            'RenewalProcess',
            'RenkoChart',
            'RepairMesh',
            'Repeated',
            'RepeatedNull',
            'RepeatedTiming',
            'RepeatingElement',
            'Replace',
            'ReplaceAll',
            'ReplaceImageValue',
            'ReplaceList',
            'ReplacePart',
            'ReplacePixelValue',
            'ReplaceRepeated',
            'RequiredPhysicalQuantities',
            'Resampling',
            'ResamplingAlgorithmData',
            'ResamplingMethod',
            'Rescale',
            'RescalingTransform',
            'ResetDirectory',
            'ResetScheduledTask',
            'ReshapeLayer',
            'Residue',
            'Resolve',
            'ResourceData',
            'ResourceObject',
            'ResourceRemove',
            'ResourceSearch',
            'ResponseForm',
            'Rest',
            'RestartInterval',
            'Restricted',
            'Resultant',
            'Return',
            'ReturnExpressionPacket',
            'ReturnPacket',
            'ReturnReceiptFunction',
            'ReturnTextPacket',
            'Reverse',
            'ReverseBiorthogonalSplineWavelet',
            'ReverseElement',
            'ReverseEquilibrium',
            'ReverseGraph',
            'ReverseUpEquilibrium',
            'RevolutionAxis',
            'RevolutionPlot3D',
            'RiccatiSolve',
            'RiceDistribution',
            'RidgeFilter',
            'RiemannR',
            'RiemannSiegelTheta',
            'RiemannSiegelZ',
            'RiemannXi',
            'Riffle',
            'Right',
            'RightArrow',
            'RightArrowBar',
            'RightArrowLeftArrow',
            'RightComposition',
            'RightCosetRepresentative',
            'RightDownTeeVector',
            'RightDownVector',
            'RightDownVectorBar',
            'RightTee',
            'RightTeeArrow',
            'RightTeeVector',
            'RightTriangle',
            'RightTriangleBar',
            'RightTriangleEqual',
            'RightUpDownVector',
            'RightUpTeeVector',
            'RightUpVector',
            'RightUpVectorBar',
            'RightVector',
            'RightVectorBar',
            'RiskAchievementImportance',
            'RiskReductionImportance',
            'RogersTanimotoDissimilarity',
            'RollPitchYawAngles',
            'RollPitchYawMatrix',
            'RomanNumeral',
            'Root',
            'RootApproximant',
            'RootIntervals',
            'RootLocusPlot',
            'RootMeanSquare',
            'RootOfUnityQ',
            'RootReduce',
            'RootSum',
            'Roots',
            'Rotate',
            'RotateLabel',
            'RotateLeft',
            'RotateRight',
            'RotationAction',
            'RotationMatrix',
            'RotationTransform',
            'Round',
            'RoundingRadius',
            'Row',
            'RowAlignments',
            'RowBox',
            'RowLines',
            'RowMinHeight',
            'RowReduce',
            'RowSpacings',
            'RowsEqual',
            'RudinShapiro',
            'RudvalisGroupRu',
            'Rule',
            'RuleDelayed',
            'RulePlot',
            'Run',
            'RunProcess',
            'RunScheduledTask',
            'RunThrough',
            'RuntimeAttributes',
            'RuntimeOptions',
            'RussellRaoDissimilarity',

            'SARIMAProcess',
            'SARMAProcess',
            'SASTriangle',
            'SSSTriangle',
            'SameQ',
            'SameTest',
            'SampleDepth',
            'SampleRate',
            'SampledSoundFunction',
            'SampledSoundList',
            'SamplingPeriod',
            'SatelliteData',
            'SatisfiabilityCount',
            'SatisfiabilityInstances',
            'SatisfiableQ',
            'Saturday',
            'Save',
            'SaveDefinitions',
            'SavitzkyGolayMatrix',
            'SawtoothWave',
            'Scale',
            'ScaleDivisions',
            'ScaleOrigin',
            'ScalePadding',
            'ScaleRangeStyle',
            'ScaleRanges',
            'Scaled',
            'ScalingFunctions',
            'ScalingMatrix',
            'ScalingTransform',
            'Scan',
            'ScheduledTask',
            'ScheduledTaskActiveQ',
            'ScheduledTaskInformation',
            'ScheduledTaskObject',
            'ScheduledTasks',
            'SchurDecomposition',
            'ScientificForm',
            'ScorerGi',
            'ScorerGiPrime',
            'ScorerHi',
            'ScorerHiPrime',
            'ScreenStyleEnvironment',
            'ScriptBaselineShifts',
            'ScriptMinSize',
            'ScriptSizeMultipliers',
            'ScrollPosition',
            'Scrollbars',
            'ScrollingOptions',
            'SearchAdjustment',
            'SearchIndexObject',
            'SearchIndices',
            'SearchQueryString',
            'SearchResultObject',
            'Sec',
            'Sech',
            'SechDistribution',
            'SectorChart',
            'SectorChart3D',
            'SectorOrigin',
            'SectorSpacing',
            'SeedRandom',
            'Select',
            'SelectComponents',
            'SelectFirst',
            'Selectable',
            'SelectedCells',
            'SelectedNotebook',
            'SelectionCreateCell',
            'SelectionEvaluate',
            'SelectionEvaluateCreateCell',
            'SelectionMove',
            'SelfLoopStyle',
            'SemanticImport',
            'SemanticImportString',
            'SemanticInterpretation',
            'SemialgebraicComponentInstances',
            'SendMail',
            'SendMessage',
            'Sequence',
            'SequenceAlignment',
            'SequenceCases',
            'SequenceCount',
            'SequenceFold',
            'SequenceFoldList',
            'SequenceHold',
            'SequencePosition',
            'Series',
            'SeriesCoefficient',
            'SeriesData',
            'ServiceConnect',
            'ServiceDisconnect',
            'ServiceExecute',
            'ServiceObject',
            'SessionTime',
            'Set',
            'SetAccuracy',
            'SetAlphaChannel',
            'SetAttributes',
            'SetCloudDirectory',
            'SetCookies',
            'SetDelayed',
            'SetDirectory',
            'SetEnvironment',
            'SetFileDate',
            'SetOptions',
            'SetPermissions',
            'SetPrecision',
            'SetProperty',
            'SetSelectedNotebook',
            'SetSharedFunction',
            'SetSharedVariable',
            'SetStreamPosition',
            'SetSystemOptions',
            'SetUsers',
            'Setter',
            'SetterBar',
            'Setting',
            'Shallow',
            'ShannonWavelet',
            'ShapiroWilkTest',
            'Share',
            'Sharpen',
            'ShearingMatrix',
            'ShearingTransform',
            'ShellRegion',
            'ShenCastanMatrix',
            'ShiftRegisterSequence',
            'ShiftedGompertzDistribution',
            'Short',
            'ShortDownArrow',
            'ShortLeftArrow',
            'ShortRightArrow',
            'ShortUpArrow',
            'Shortest',
            'ShortestPathFunction',
            'Show',
            'ShowAutoSpellCheck',
            'ShowAutoStyles',
            'ShowCellBracket',
            'ShowCellLabel',
            'ShowCellTags',
            'ShowCursorTracker',
            'ShowGroupOpener',
            'ShowPageBreaks',
            'ShowSelection',
            'ShowSpecialCharacters',
            'ShowStringCharacters',
            'ShrinkingDelay',
            'SiderealTime',
            'SiegelTheta',
            'SiegelTukeyTest',
            'Sign',
            'SignPadding',
            'SignTest',
            'Signature',
            'SignedRankTest',
            'SignedRegionDistance',
            'SignificanceLevel',
            'SimilarityRules',
            'SimpleGraph',
            'SimpleGraphQ',
            'Simplex',
            'Simplify',
            'Sin',
            'SinIntegral',
            'Sinc',
            'SinghMaddalaDistribution',
            'SingleLetterItalics',
            'SingularValueDecomposition',
            'SingularValueList',
            'SingularValuePlot',
            'Sinh',
            'SinhIntegral',
            'SixJSymbol',
            'Skeleton',
            'SkeletonTransform',
            'SkellamDistribution',
            'SkewNormalDistribution',
            'Skewness',
            'SkinStyle',
            'Skip',
            'SliceContourPlot3D',
            'SliceDensityPlot3D',
            'SliceDistribution',
            'SliceVectorPlot3D',
            'SlideView',
            'Slider',
            'Slider2D',
            'Slot',
            'SlotSequence',
            'Small',
            'SmallCircle',
            'Smaller',
            'SmithDecomposition',
            'SmithDelayCompensator',
            'SmithWatermanSimilarity',
            'SmoothDensityHistogram',
            'SmoothHistogram',
            'SmoothHistogram3D',
            'SmoothKernelDistribution',
            'Snippet',
            'SocialMediaData',
            'SocketConnect',
            'SocketObject',
            'SoftmaxLayer',
            'SokalSneathDissimilarity',
            'SolarEclipse',
            'SolarSystemFeatureData',
            'SolidData',
            'SolidRegionQ',
            'Solve',
            'SolveAlways',
            'Sort',
            'SortBy',
            'Sound',
            'SoundNote',
            'SoundVolume',
            'SourceLink',
            'Sow',
            'SpaceCurveData',
            'Spacer',
            'Spacings',
            'Span',
            'SpanFromAbove',
            'SpanFromBoth',
            'SpanFromLeft',
            'SparseArray',
            'SpatialGraphDistribution',
            'Speak',
            'SpearmanRankTest',
            'SpearmanRho',
            'SpeciesData',
            'SpecificityGoal',
            'Spectrogram',
            'SpectrogramArray',
            'Specularity',
            'SpellingCorrection',
            'SpellingCorrectionList',
            'SpellingOptions',
            'Sphere',
            'SphericalBesselJ',
            'SphericalBesselY',
            'SphericalHankelH1',
            'SphericalHankelH2',
            'SphericalHarmonicY',
            'SphericalPlot3D',
            'SphericalRegion',
            'SphericalShell',
            'SpheroidalEigenvalue',
            'SpheroidalJoiningFactor',
            'SpheroidalPS',
            'SpheroidalPSPrime',
            'SpheroidalQS',
            'SpheroidalQSPrime',
            'SpheroidalRadialFactor',
            'SpheroidalS1',
            'SpheroidalS1Prime',
            'SpheroidalS2',
            'SpheroidalS2Prime',
            'SplicedDistribution',
            'SplineClosed',
            'SplineDegree',
            'SplineKnots',
            'SplineWeights',
            'Split',
            'SplitBy',
            'SpokenString',
            'Sqrt',
            'SqrtBox',
            'SqrtBoxOptions',
            'Square',
            'SquareFreeQ',
            'SquareIntersection',
            'SquareMatrixQ',
            'SquareSubset',
            'SquareSubsetEqual',
            'SquareSuperset',
            'SquareSupersetEqual',
            'SquareUnion',
            'SquareWave',
            'SquaredEuclideanDistance',
            'SquaresR',
            'StabilityMargins',
            'StabilityMarginsStyle',
            'StableDistribution',
            'Stack',
            'StackBegin',
            'StackComplete',
            'StackInhibit',
            'StadiumShape',
            'StandardAtmosphereData',
            'StandardDeviation',
            'StandardDeviationFilter',
            'StandardForm',
            'StandardOceanData',
            'Standardize',
            'Standardized',
            'StandbyDistribution',
            'Star',
            'StarClusterData',
            'StarData',
            'StarGraph',
            'StartAsynchronousTask',
            'StartOfLine',
            'StartOfString',
            'StartProcess',
            'StartScheduledTask',
            'StartingStepSize',
            'StateFeedbackGains',
            'StateOutputEstimator',
            'StateResponse',
            'StateSpaceModel',
            'StateSpaceRealization',
            'StateSpaceTransform',
            'StateTransformationLinearize',
            'StationaryDistribution',
            'StationaryWaveletPacketTransform',
            'StationaryWaveletTransform',
            'StatusArea',
            'StatusCentrality',
            'StepMonitor',
            'StieltjesGamma',
            'StirlingS1',
            'StirlingS2',
            'StopAsynchronousTask',
            'StopScheduledTask',
            'StoppingPowerData',
            'StrataVariables',
            'StratonovichProcess',
            'StreamColorFunction',
            'StreamColorFunctionScaling',
            'StreamDensityPlot',
            'StreamPlot',
            'StreamPoints',
            'StreamPosition',
            'StreamScale',
            'StreamStyle',
            'Streams',
            'String',
            'StringCases',
            'StringContainsQ',
            'StringCount',
            'StringDelete',
            'StringDrop',
            'StringEndsQ',
            'StringExpression',
            'StringExtract',
            'StringForm',
            'StringFormat',
            'StringFreeQ',
            'StringInsert',
            'StringJoin',
            'StringLength',
            'StringMatchQ',
            'StringPadLeft',
            'StringPadRight',
            'StringPart',
            'StringPartition',
            'StringPosition',
            'StringQ',
            'StringRepeat',
            'StringReplace',
            'StringReplaceList',
            'StringReplacePart',
            'StringReverse',
            'StringRiffle',
            'StringRotateLeft',
            'StringRotateRight',
            'StringSkeleton',
            'StringSplit',
            'StringStartsQ',
            'StringTake',
            'StringTemplate',
            'StringToStream',
            'StringTrim',
            'StripBoxes',
            'StripOnInput',
            'StripWrapperBoxes',
            'StructuralImportance',
            'StructuredArray',
            'StructuredSelection',
            'StruveH',
            'StruveL',
            'Stub',
            'StudentTDistribution',
            'Style',
            'StyleBox',
            'StyleData',
            'StyleDefinitions',
            'SubMinus',
            'SubPlus',
            'SubStar',
            'Subdivide',
            'Subfactorial',
            'Subgraph',
            'SubresultantPolynomialRemainders',
            'SubresultantPolynomials',
            'Subresultants',
            'Subscript',
            'SubscriptBox',
            'SubscriptBoxOptions',
            'Subsequences',
            'Subset',
            'SubsetEqual',
            'SubsetQ',
            'Subsets',
            'SubstitutionSystem',
            'Subsuperscript',
            'SubsuperscriptBox',
            'SubsuperscriptBoxOptions',
            'Subtract',
            'SubtractFrom',
            'Succeeds',
            'SucceedsEqual',
            'SucceedsSlantEqual',
            'SucceedsTilde',
            'SuchThat',
            'Sum',
            'SumConvergence',
            'SummationLayer',
            'SunPosition',
            'Sunday',
            'Sunrise',
            'Sunset',
            'SuperDagger',
            'SuperMinus',
            'SuperPlus',
            'SuperStar',
            'SupernovaData',
            'Superscript',
            'SuperscriptBox',
            'SuperscriptBoxOptions',
            'Superset',
            'SupersetEqual',
            'Surd',
            'SurfaceData',
            'SurvivalDistribution',
            'SurvivalFunction',
            'SurvivalModel',
            'SurvivalModelFit',
            'SuzukiDistribution',
            'SuzukiGroupSuz',
            'SwatchLegend',
            'Switch',
            'Symbol',
            'SymbolName',
            'SymletWavelet',
            'Symmetric',
            'SymmetricGroup',
            'SymmetricKey',
            'SymmetricMatrixQ',
            'SymmetricPolynomial',
            'SymmetricReduction',
            'Symmetrize',
            'SymmetrizedArray',
            'SymmetrizedArrayRules',
            'SymmetrizedDependentComponents',
            'SymmetrizedIndependentComponents',
            'SymmetrizedReplacePart',
            'SynchronousInitialization',
            'SynchronousUpdating',
            'SyntaxForm',
            'SyntaxInformation',
            'SyntaxLength',
            'SyntaxPacket',
            'SyntaxQ',
            'SystemDialogInput',
            'SystemInformation',
            'SystemOpen',
            'SystemOptions',
            'SystemsModelDelay',
            'SystemsModelDelayApproximate',
            'SystemsModelDelete',
            'SystemsModelDimensions',
            'SystemsModelExtract',
            'SystemsModelFeedbackConnect',
            'SystemsModelLabels',
            'SystemsModelLinearity',
            'SystemsModelMerge',
            'SystemsModelOrder',
            'SystemsModelParallelConnect',
            'SystemsModelSeriesConnect',
            'SystemsModelStateFeedbackConnect',
            'SystemsModelVectorRelativeOrders',

            'TTest',
            'TabView',
            'Table',
            'TableAlignments',
            'TableDepth',
            'TableDirections',
            'TableForm',
            'TableHeadings',
            'TableSpacing',
            'TagBox',
            'TagBoxOptions',
            'TagSet',
            'TagSetDelayed',
            'TagUnset',
            'TaggingRules',
            'Take',
            'TakeDrop',
            'TakeLargest',
            'TakeLargestBy',
            'TakeSmallest',
            'TakeSmallestBy',
            'TakeWhile',
            'Tally',
            'Tan',
            'Tanh',
            'TargetDevice',
            'TargetFunctions',
            'TargetUnits',
            'TautologyQ',
            'TeXForm',
            'TelegraphProcess',
            'TemplateApply',
            'TemplateBox',
            'TemplateBoxOptions',
            'TemplateExpression',
            'TemplateIf',
            'TemplateObject',
            'TemplateSequence',
            'TemplateSlot',
            'TemplateWith',
            'TemporalData',
            'TemporalRegularity',
            'Temporary',
            'TensorContract',
            'TensorDimensions',
            'TensorExpand',
            'TensorProduct',
            'TensorRank',
            'TensorReduce',
            'TensorSymmetry',
            'TensorTranspose',
            'TensorWedge',
            'TestID',
            'TestReport',
            'TestReportObject',
            'TestResultObject',
            'Tetrahedron',
            'Text',
            'TextAlignment',
            'TextCases',
            'TextCell',
            'TextClipboardType',
            'TextData',
            'TextElement',
            'TextGrid',
            'TextJustification',
            'TextPacket',
            'TextPosition',
            'TextRecognize',
            'TextSearch',
            'TextSearchReport',
            'TextSentences',
            'TextString',
            'TextStructure',
            'TextWords',
            'Texture',
            'TextureCoordinateFunction',
            'TextureCoordinateScaling',
            'Therefore',
            'ThermodynamicData',
            'ThermometerGauge',
            'Thick',
            'Thickness',
            'Thin',
            'Thinning',
            'ThompsonGroupTh',
            'Thread',
            'ThreeJSymbol',
            'Threshold',
            'Through',
            'Throw',
            'ThueMorse',
            'Thumbnail',
            'Thursday',
            'Ticks',
            'TicksStyle',
            'Tilde',
            'TildeEqual',
            'TildeFullEqual',
            'TildeTilde',
            'TimeConstrained',
            'TimeConstraint',
            'TimeDirection',
            'TimeFormat',
            'TimeObject',
            'TimeObjectQ',
            'TimeSeries',
            'TimeSeriesAggregate',
            'TimeSeriesForecast',
            'TimeSeriesInsert',
            'TimeSeriesInvertibility',
            'TimeSeriesMap',
            'TimeSeriesMapThread',
            'TimeSeriesModel',
            'TimeSeriesModelFit',
            'TimeSeriesResample',
            'TimeSeriesRescale',
            'TimeSeriesShift',
            'TimeSeriesThread',
            'TimeSeriesWindow',
            'TimeUsed',
            'TimeValue',
            'TimeZone',
            'TimeZoneConvert',
            'TimeZoneOffset',
            'TimelinePlot',
            'Times',
            'TimesBy',
            'Timing',
            'Tiny',
            'TitsGroupT',
            'ToBoxes',
            'ToCharacterCode',
            'ToContinuousTimeModel',
            'ToDiscreteTimeModel',
            'ToEntity',
            'ToExpression',
            'ToInvertibleTimeSeries',
            'ToLowerCase',
            'ToNumberField',
            'ToPolarCoordinates',
            'ToRadicals',
            'ToRules',
            'ToSphericalCoordinates',
            'ToString',
            'ToUpperCase',
            'Today',
            'ToeplitzMatrix',
            'Together',
            'Toggler',
            'TogglerBar',
            'TokenWords',
            'Tolerance',
            'Tomorrow',
            'Tooltip',
            'TooltipDelay',
            'TooltipStyle',
            'Top',
            'TopHatTransform',
            'TopologicalSort',
            'Total',
            'TotalLayer',
            'TotalVariationFilter',
            'TotalWidth',
            'TouchPosition',
            'TouchscreenAutoZoom',
            'TouchscreenControlPlacement',
            'Tr',
            'Trace',
            'TraceAbove',
            'TraceBackward',
            'TraceDepth',
            'TraceDialog',
            'TraceForward',
            'TraceOff',
            'TraceOn',
            'TraceOriginal',
            'TracePrint',
            'TraceScan',
            'TrackedSymbols',
            'TrackingFunction',
            'TracyWidomDistribution',
            'TradingChart',
            'TraditionalForm',
            'TransferFunctionCancel',
            'TransferFunctionExpand',
            'TransferFunctionFactor',
            'TransferFunctionModel',
            'TransferFunctionPoles',
            'TransferFunctionTransform',
            'TransferFunctionZeros',
            'TransformationClass',
            'TransformationFunction',
            'TransformationFunctions',
            'TransformationMatrix',
            'TransformedDistribution',
            'TransformedField',
            'TransformedProcess',
            'TransformedRegion',
            'TransitionDirection',
            'TransitionDuration',
            'TransitionEffect',
            'TransitiveClosureGraph',
            'TransitiveReductionGraph',
            'Translate',
            'TranslationOptions',
            'TranslationTransform',
            'Transliterate',
            'Transparent',
            'Transpose',
            'TravelDirections',
            'TravelDirectionsData',
            'TravelDistance',
            'TravelDistanceList',
            'TravelMethod',
            'TravelTime',
            'TreeForm',
            'TreeGraph',
            'TreeGraphQ',
            'TreePlot',
            'TrendStyle',
            'Triangle',
            'TriangleWave',
            'TriangularDistribution',
            'TriangulateMesh',
            'Trig',
            'TrigExpand',
            'TrigFactor',
            'TrigFactorList',
            'TrigReduce',
            'TrigToExp',
            'Trigger',
            'TrimmedMean',
            'TropicalStormData',
            'True',
            'TrueQ',
            'TruncatedDistribution',
            'TsallisQExponentialDistribution',
            'TsallisQGaussianDistribution',
            'Tube',
            'Tuesday',
            'TukeyLambdaDistribution',
            'TukeyWindow',
            'TunnelData',
            'Tuples',
            'TuranGraph',
            'TuringMachine',
            'TuttePolynomial',

            'URL',
            'URLBuild',
            'URLDecode',
            'URLDispatcher',
            'URLDownload',
            'URLEncode',
            'URLExecute',
            'URLExpand',
            'URLParse',
            'URLQueryDecode',
            'URLQueryEncode',
            'URLRead',
            'URLSaveAsynchronous',
            'URLShorten',
            'URLSubmit',
            'UnateQ',
            'Uncompress',
            'Undefined',
            'UnderBar',
            'Underflow',
            'Underlined',
            'Underoverscript',
            'UnderoverscriptBox',
            'UnderoverscriptBoxOptions',
            'Underscript',
            'UnderscriptBox',
            'UnderscriptBoxOptions',
            'UnderseaFeatureData',
            'UndirectedEdge',
            'UndirectedGraph',
            'UndirectedGraphQ',
            'UndoOptions',
            'UndoTrackedVariables',
            'Unequal',
            'UnequalTo',
            'Unevaluated',
            'UniformDistribution',
            'UniformGraphDistribution',
            'UniformSumDistribution',
            'Uninstall',
            'Union',
            'UnionPlus',
            'Unique',
            'UnitBox',
            'UnitConvert',
            'UnitDimensions',
            'UnitRootTest',
            'UnitSimplify',
            'UnitStep',
            'UnitSystem',
            'UnitTriangle',
            'UnitVector',
            'UnitaryMatrixQ',
            'Unitize',
            'UnityDimensions',
            'UniverseModelData',
            'UniversityData',
            'UnixTime',
            'Unprotect',
            'UnsameQ',
            'UnsavedVariables',
            'Unset',
            'UnsetShared',
            'UpArrow',
            'UpArrowBar',
            'UpArrowDownArrow',
            'UpDownArrow',
            'UpEquilibrium',
            'UpSet',
            'UpSetDelayed',
            'UpTee',
            'UpTeeArrow',
            'UpTo',
            'UpValues',
            'Update',
            'UpdateInterval',
            'UpdateSearchIndex',
            'UpperCaseQ',
            'UpperLeftArrow',
            'UpperRightArrow',
            'UpperTriangularize',
            'Upsample',
            'UsingFrontEnd',
            'UtilityFunction',

            'ValidationLength',
            'ValidationSet',
            'ValueDimensions',
            'ValueQ',
            'Values',
            'Variables',
            'Variance',
            'VarianceEquivalenceTest',
            'VarianceEstimatorFunction',
            'VarianceGammaDistribution',
            'VarianceTest',
            'VectorAngle',
            'VectorColorFunction',
            'VectorColorFunctionScaling',
            'VectorDensityPlot',
            'VectorPlot',
            'VectorPlot3D',
            'VectorPoints',
            'VectorQ',
            'VectorScale',
            'VectorStyle',
            'Vectors',
            'Vee',
            'Verbatim',
            'VerificationTest',
            'VerifyConvergence',
            'VerifySecurityCertificates',
            'VerifySolutions',
            'VerifyTestAssumptions',
            'VertexAdd',
            'VertexCapacity',
            'VertexColors',
            'VertexComponent',
            'VertexConnectivity',
            'VertexContract',
            'VertexCoordinateRules',
            'VertexCoordinates',
            'VertexCorrelationSimilarity',
            'VertexCosineSimilarity',
            'VertexCount',
            'VertexCoverQ',
            'VertexDataCoordinates',
            'VertexDegree',
            'VertexDelete',
            'VertexDiceSimilarity',
            'VertexEccentricity',
            'VertexInComponent',
            'VertexInDegree',
            'VertexIndex',
            'VertexJaccardSimilarity',
            'VertexLabelStyle',
            'VertexLabeling',
            'VertexLabels',
            'VertexList',
            'VertexNormals',
            'VertexOutComponent',
            'VertexOutDegree',
            'VertexQ',
            'VertexRenderingFunction',
            'VertexReplace',
            'VertexShape',
            'VertexShapeFunction',
            'VertexSize',
            'VertexStyle',
            'VertexTextureCoordinates',
            'VertexWeight',
            'VerticalBar',
            'VerticalGauge',
            'VerticalSeparator',
            'VerticalSlider',
            'VerticalTilde',
            'ViewAngle',
            'ViewCenter',
            'ViewMatrix',
            'ViewPoint',
            'ViewRange',
            'ViewVector',
            'ViewVertical',
            'Visible',
            'VoigtDistribution',
            'VolcanoData',
            'Volume',
            'VonMisesDistribution',
            'VoronoiMesh',

            'WaitAll',
            'WaitAsynchronousTask',
            'WaitNext',
            'WakebyDistribution',
            'WalleniusHypergeometricDistribution',
            'WaringYuleDistribution',
            'WarpingCorrespondence',
            'WarpingDistance',
            'WatershedComponents',
            'WatsonUSquareTest',
            'WattsStrogatzGraphDistribution',
            'WaveletBestBasis',
            'WaveletFilterCoefficients',
            'WaveletImagePlot',
            'WaveletListPlot',
            'WaveletMapIndexed',
            'WaveletMatrixPlot',
            'WaveletPhi',
            'WaveletPsi',
            'WaveletScale',
            'WaveletScalogram',
            'WaveletThreshold',
            'WeakStationarity',
            'WeaklyConnectedComponents',
            'WeaklyConnectedGraphComponents',
            'WeaklyConnectedGraphQ',
            'WeatherData',
            'WeatherForecastData',
            'WeberE',
            'Wedge',
            'Wednesday',
            'WeibullDistribution',
            'WeierstrassHalfPeriods',
            'WeierstrassInvariants',
            'WeierstrassP',
            'WeierstrassPPrime',
            'WeierstrassSigma',
            'WeierstrassZeta',
            'WeightedAdjacencyGraph',
            'WeightedAdjacencyMatrix',
            'WeightedData',
            'WeightedGraphQ',
            'Weights',
            'WelchWindow',
            'WheelGraph',
            'WhenEvent',
            'Which',
            'While',
            'White',
            'WhiteNoiseProcess',
            'WhitePoint',
            'Whitespace',
            'WhitespaceCharacter',
            'WhittakerM',
            'WhittakerW',
            'WienerFilter',
            'WienerProcess',
            'WignerD',
            'WignerSemicircleDistribution',
            'WikipediaData',
            'WikipediaSearch',
            'WilksW',
            'WilksWTest',
            'WindDirectionData',
            'WindSpeedData',
            'WindVectorData',
            'WindowClickSelect',
            'WindowElements',
            'WindowFloating',
            'WindowFrame',
            'WindowMargins',
            'WindowMovable',
            'WindowOpacity',
            'WindowSize',
            'WindowStatusArea',
            'WindowTitle',
            'WindowToolbars',
            'WishartMatrixDistribution',
            'With',
            'WolframAlpha',
            'WolframLanguageData',
            'Word',
            'WordBoundary',
            'WordCharacter',
            'WordCloud',
            'WordCount',
            'WordCounts',
            'WordData',
            'WordDefinition',
            'WordFrequency',
            'WordFrequencyData',
            'WordList',
            'WordOrientation',
            'WordSearch',
            'WordSeparators',
            'WordSpacings',
            'WordStem',
            'WordTranslation',
            'WorkingPrecision',
            'WrapAround',
            'Write',
            'WriteLine',
            'WriteString',
            'Wronskian',

            'XMLElement',
            'XMLObject',
            'XMLTemplate',
            'XYZColor',
            'Xnor',
            'Xor',

            'Yellow',
            'Yesterday',
            'YuleDissimilarity',

            'ZIPCodeData',
            'ZTest',
            'ZTransform',
            'ZernikeR',
            'ZeroSymmetric',
            'ZeroTest',
            'Zeta',
            'ZoomCenter',
            'ZetaZero',
            'ZipfDistribution',
            'ZoomFactor'
        )
    ),
    'CASE_SENSITIVE' => array(
        1 => true
    ),
    'SYMBOLS' => array(
        1 => array(
            '@', '//', '/@', '@@', '@@@', '#', '&', //functions
            '=', ':=', '+=', '-=', '*=', '/=', //assignments
            '^', '+' ,'-', '/', '*', '>', '<', '%', //math
            '||', '&&', //logic
            '/.', '/;', '->', ':>' //rules
        ),
        2 => array( // Wolfram
            '$Aborted',
            '$AllowExternalChannelFunctions',
            '$AssertFunction',
            '$Assumptions',
            '$AsynchronousTask',
            '$AudioOutputDevices',

            '$BaseDirectory',
            '$BatchInput',
            '$BatchOutput',
            '$ByteOrdering',

            '$CacheBaseDirectory',
            '$Canceled',
            '$ChannelBase',
            '$CharacterEncoding',
            '$CharacterEncodings',
            '$CloudBase',
            '$CloudConnected',
            '$CloudCreditsAvailable',
            '$CloudEvaluation',
            '$CloudExpressionBase',
            '$CloudRootDirectory',
            '$CloudSymbolBase',
            '$CommandLine',
            '$CompilationTarget',
            '$ConfiguredKernels',
            '$Context',
            '$ContextPath',
            '$ControlActiveSetting',
            '$CookieStore',
            '$Cookies',
            '$CreationDate',
            '$CurrentLink',

            '$DateStringFormat',
            '$DefaultAudioOutputDevice',
            '$DefaultImagingDevice',
            '$DefaultLocalBase',
            '$Display',
            '$DisplayFunction',
            '$DistributedContexts',
            '$DynamicEvaluation',

            '$Echo',
            '$EmbedCodeEnvironments',
            '$EmbeddableServices',
            '$EntityStores',
            '$Epilog',
            '$EvaluationCloudObject',
            '$EvaluationEnvironment',
            '$ExportFormats',

            '$Failed',
            '$FontFamilies',
            '$FrontEnd',
            '$FrontEndSession',

            '$GeoLocation',
            '$GeoLocationCity',
            '$GeoLocationCountry',
            '$GeoLocationSource',

            '$HistoryLength',
            '$HomeDirectory',

            '$IgnoreEOF',
            '$ImageFormattingWidth',
            '$ImagingDevice',
            '$ImagingDevices',
            '$ImportFormats',
            '$InitialDirectory',
            '$Input',
            '$InputFileName',
            '$InputStreamMethods',
            '$Inspector',
            '$InstallationDirectory',
            '$InterpreterTypes',
            '$IterationLimit',

            '$KernelCount',
            '$KernelID',

            '$Language',
            '$LibraryPath',
            '$LicenseExpirationDate',
            '$LicenseID',
            '$LicenseServer',
            '$Line',
            '$Linked',
            '$LocalBase',
            '$LocalSymbolBase',

            '$MachineAddresses',
            '$MachineDomains',
            '$MachineEpsilon',
            '$MachineID',
            '$MachineName',
            '$MachinePrecision',
            '$MachineType',
            '$MaxExtraPrecision',
            '$MaxMachineNumber',
            '$MaxNumber',
            '$MaxPiecewiseCases',
            '$MaxPrecision',
            '$MaxRootDegree',
            '$MessageGroups',
            '$MessageList',
            '$MessagePrePrint',
            '$Messages',
            '$MinMachineNumber',
            '$MinNumber',
            '$MinPrecision',
            '$ModuleNumber',

            '$NewMessage',
            '$NewSymbol',
            '$Notebooks',
            '$NumberMarks',

            '$OperatingSystem',
            '$Output',
            '$OutputSizeLimit',
            '$OutputStreamMethods',

            '$Packages',
            '$ParentLink',
            '$ParentProcessID',
            '$Path',
            '$PathnameSeparator',
            '$PerformanceGoal',
            '$Permissions',
            '$PlotTheme',
            '$Post',
            '$Pre',
            '$PrePrint',
            '$PreRead',
            '$Printout3DPreviewer',
            '$ProcessID',
            '$ProcessorCount',
            '$ProcessorType',

            '$RecursionLimit',
            '$ReleaseNumber',
            '$RequesterAddress',
            '$RequesterWolframID',
            '$RequesterWolframUUID',
            '$RootDirectory',

            '$ScheduledTask',
            '$ScriptCommandLine',
            '$ScriptInputString',
            '$Services',
            '$SessionID',
            '$SharedFunctions',
            '$SharedVariables',
            '$SoundDisplayFunction',
            '$SourceLink',
            '$SynchronousEvaluation',
            '$SyntaxHandler',
            '$System',
            '$SystemCharacterEncoding',
            '$SystemID',
            '$SystemShell',
            '$SystemTimeZone',
            '$SystemWordLength',

            '$TemplatePath',
            '$TemporaryDirectory',
            '$TimeUnit',
            '$TimeZone',
            '$TimeZoneEntity',
            '$TimedOut',

            '$UnitSystem',
            '$Urgent',
            '$UserAgentString',
            '$UserBaseDirectory',
            '$UserDocumentsDirectory',
            '$UserName',

            '$Version',
            '$VersionNumber',

            '$WolframID',
            '$WolframUUID',
        ),
        3 => array( // array stuff
            '{', '}', '[[', ']]', '<|', '|>'
        ),
    ),
    'STYLES' => array(
        'KEYWORDS' => array(
            1 => 'color: #FF7700; font-weight: bold;',
        ),
        'COMMENTS' => array(
            'MULTI' => 'color: #999999; font-style: italic;',
        ),
        'ESCAPE_CHAR' => array(
            1 => '',
        ),
        'BRACKETS' => array(),
        'STRINGS' => array(
            1 => 'color: #666666; font-style: italic;',
        ),
        'NUMBERS' => array(
        ),
        'METHODS' => array(),
        'SYMBOLS' => array(
            1 => 'color: #133CAC; font-weight: bold;',
            2 => 'color: #028E9B; font-weight: bold;',
            3 => 'color: #FFAD00; font-weight: bold;',
        ),
        //'BRACKETS' => array(
        //0 => 'color: #8C0953; font-weight: bold;', // array
        //),
        'REGEXPS' => array(
            0 => 'color: #133CAC; font-weight: bold;',
        ),
        'SCRIPT' => array()
    ),
    'URLS' => array(
        1 => 'http://reference.wolfram.com/language/ref/{FNAME}.html'
    ),
    'OOLANG' => false,
    'OBJECT_SPLITTERS' => array(),
    'REGEXPS' => array(
        0 => '#[1-9][0-9]*'
    ),
    'STRICT_MODE_APPLIES' => GESHI_MAYBE,
    'SCRIPT_DELIMITERS' => array(),
    'HIGHLIGHT_STRICT_BLOCK' => array(),
    'TAB_WIDTH' => 4
);
