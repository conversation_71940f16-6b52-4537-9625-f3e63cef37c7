<?php
/*************************************************************************************
 * texgraph.php
 * -----------
 * Author: <PERSON> (<EMAIL>)
 * Copyright: (c) 2011 <PERSON>
 * Release Version: 1.0.8.13
 * Date Started: 2011-09-18
 *
 * TeXgraph language file for GeSHi.
 *
 * http://texgraph.tuxfamily.org/
 *
 *************************************************************************************
 *
 *     This file is part of GeSHi.
 *
 *   GeSHi is free software; you can redistribute it and/or modify
 *   it under the terms of the GNU General Public License as published by
 *   the Free Software Foundation; either version 2 of the License, or
 *   (at your option) any later version.
 *
 *   GeSHi is distributed in the hope that it will be useful,
 *   but WITHOUT ANY WARRANTY; without even the implied warranty of
 *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *   GNU General Public License for more details.
 *
 *   You should have received a copy of the GNU General Public License
 *   along with GeSHi; if not, write to the Free Software
 *   Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 ************************************************************************************/

$language_data = array (
    'LANG_NAME' => 'TeXgraph',
    'COMMENT_SINGLE' => array(1 => '//'),
    'COMMENT_MULTI' => array(
        '{'=>'}'
    ),
    'COMMENT_REGEXP' => array(
    ),
    'CASE_KEYWORDS' => GESHI_CAPS_NO_CHANGE,
    'QUOTEMARKS' => array('"'),
    'ESCAPE_CHAR' => '',
    'KEYWORDS' => array(
        1 => array( //file construction
            'TeXgraph', 'Cmd', 'Var', 'Mac', 'Graph', 'Include'
        ),
        2 => array( //programmation structure
            'if', 'else', 'elif',  'fi',
            'for', 'do', 'od', 'by', 'By',
            'step', 'until', 'in', 'to', 'repeat', 'from', 'odfi', 'andif',
            'And', 'Or', 'CutA', 'CutB', 'Inside', 'Inter', 'InterL'
        ),
        3 => array( // commandes prédéfinies
            'Aretes',
            'Arg',
            'Args',
            'Assign',
            'Axes',
            'Bezier',
            'Bord',
            'Border',
            'Build3D',
            'Cartesienne',
            'ChangeAttr',
            'Clip2D',
            'Clip3DLine',
            'ClipFacet',
            'Close',
            'CloseFile',
            'ComposeMatrix',
            'ComposeMatrix3D',
            'Concat',
            'ConvertToObj',
            'ConvertToObjN',
            'Copy',
            'Courbe',
            'Creer',
            'DefMac',
            'DefVar',
            'DefaultAttr',
            'Del',
            'DelGraph',
            'DelMac',
            'DelVar',
            'Der',
            'Diff',
            'Display3D',
            'DistCam',
            'Droite',
            'Dup',
            'Echange',
            'Ellipse',
            'EllipticArc',
            'Ent',
            'EpsCoord',
            'EquaDif',
            'Eval',
            'Exec',
            'Export',
            'ExportObject',
            'ExportPathData',
            'Fenetre',
            'FileExists',
            'For',
            'Free',
            'Fvisible',
            'Get',
            'Get3D',
            'GetAttr',
            'GetMatrix',
            'GetMatrix3D',
            'GetSpline',
            'GetStr',
            'GetSurface',
            'GrayScale',
            'Grille',
            'HexaColor',
            'IdMatrix',
            'IdMatrix3D',
            'Im',
            'Implicit',
            'Inc',
            'Input',
            'InputMac',
            'Inserer3D',
            'Insert',
            'Int',
            'IsMac',
            'IsString',
            'IsVar',
            'Label',
            'Ligne',
            'Liste',
            'Load',
            'LoadImage',
            'Loop',
            'LowerCase',
            'M',
            'MakePoly',
            'Map',
            'Marges',
            'Merge',
            'Message',
            'Mix',
            'ModelView',
            'Mtransform',
            'Mtransform3D',
            'MyExport',
            'Nargs',
            'NewGraph',
            'NewMac',
            'NewVar',
            'Nops',
            'Norm',
            'Normal',
            'OpenFile',
            'OriginalCoord',
            'PaintFacet',
            'PaintVertex',
            'Path',
            'PermuteWith',
            'Point',
            'Polaire',
            'PosCam',
            'Prodscal',
            'Prodvec',
            'Proj3D',
            'Rand',
            'Re',
            'ReCalc',
            'ReadData',
            'ReadFlatPs',
            'ReadObj',
            'RenCommand',
            'RenMac',
            'RestoreAttr',
            'Reverse',
            'Rgb',
            'Round',
            'Saut',
            'SaveAttr',
            'ScientificF',
            'Seq',
            'Set',
            'SetAttr',
            'SetMatrix',
            'SetMatrix3D',
            'Si',
            'Solve',
            'Sommets',
            'Sort',
            'SortFacet',
            'Special',
            'Spline',
            'Str',
            'StrArgs',
            'StrComp',
            'StrCopy',
            'StrDel',
            'StrEval',
            'StrLength',
            'StrPos',
            'StrReplace',
            'String',
            'TeX2FlatPs',
            'UpperCase',
            'While',
            'WriteFile',
            'abs',
            'arccos',
            'arccot',
            'arcsin',
            'arctan',
            'argch',
            'argcth',
            'argsh',
            'argth',
            'bar',
            'ch',
            'cos',
            'cot',
            'cth',
            'draw',
            'opp',
            'sh',
            'sin',
            'sqr',
            'sqrt',
            'tan',
            'th',
        ),
        4 => array( //commandes relatives à l'interface graphique
            'AddMenu2D',
            'AddMenu3D',
            'Attributs',
            'DelBitmap',
            'DelButton',
            'DelItem',
            'DelText',
            'Delay',
            'Hide',
            'ListFiles',
            'ListWords',
            'MaxPixels',
            'Move',
            'NewBitmap',
            'NewButton',
            'NewItem',
            'NewText',
            'NotXor',
            'Pixel',
            'Pixel2Scr',
            'ReDraw',
            'Scr2Pixel',
            'Show',
            'Stroke',
            'Timer',
            'TimerMac',
            'UpdateLocalDatabase',
            'VisibleGraph',
        ),
        5 => array( //constantes prédéfinies
            'Data',
            'Diese',
            'DirSep',
            'DocPath',
            'ExportMode',
            'GUI',
            'Huge',
            'ImageViewer',
            'InitialPath',
            'JavaviewPath',
            'LARGE',
            'LF',
            'Large',
            'Nil',
            'PdfReader',
            'Thicklines',
            'TmpPath',
            'UserMacPath',
            'WebLoad',
            'Windows',
            'Xmax',
            'Xmin',
            'Xscale',
            'Ymax',
            'Ymin',
            'Yscale',
            'aliceblue',
            'antiquewhite',
            'aqua',
            'aquamarine',
            'asterisk',
            'azure',
            'baseline',
            'bdiag',
            'beige',
            'bevel',
            'bezier',
            'bigdot',
            'bisque',
            'black',
            'blanchedalmond',
            'blue',
            'blueviolet',
            'bmp',
            'bottom',
            'brown',
            'burlywood',
            'butt',
            'cadetblue',
            'center',
            'centered',
            'central',
            'chartreuse',
            'chocolate',
            'circle',
            'closepath',
            'comp',
            'coral',
            'cornflowerblue',
            'cornsilk',
            'crimson',
            'cross',
            'curve',
            'cyan',
            'darkblue',
            'darkcyan',
            'darkgoldenrod',
            'darkgray',
            'darkgreen',
            'darkkhaki',
            'darkmagenta',
            'darkolivegreen',
            'darkorange',
            'darkorchid',
            'darkred',
            'darksalmon',
            'darkseagreen',
            'darkslateblue',
            'darkslategray',
            'darkturquoise',
            'darkviolet',
            'dashed',
            'deeppink',
            'deepskyblue',
            'diagcross',
            'diamond',
            'dimgray',
            'dodgerblue',
            'dot',
            'dotcircle',
            'dotted',
            'e',
            'ellipse',
            'ellipticArc',
            'eps',
            'epsc',
            'fdiag',
            'firebrick',
            'floralwhite',
            'footnotesize',
            'forestgreen',
            'framed',
            'fuchsia',
            'full',
            'gainsboro',
            'geom',
            'ghostwhite',
            'gold',
            'goldenrod',
            'gray',
            'green',
            'greenyellow',
            'honeydew',
            'horizontal',
            'hotpink',
            'huge',
            'hvcross',
            'i',
            'indianred',
            'indigo',
            'ivory',
            'jump',
            'jvx',
            'khaki',
            'large',
            'lavender',
            'lavenderblush',
            'lawngreen',
            'left',
            'lemonchiffon',
            'lightblue',
            'lightcoral',
            'lightcyan',
            'lightgoldenrodyellow',
            'lightgray',
            'lightgreen',
            'lightpink',
            'lightsalmon',
            'lightseagreen',
            'lightskyblue',
            'lightslategray',
            'lightsteelblue',
            'lightyellow',
            'lime',
            'limegreen',
            'line',
            'linearc',
            'linen',
            'magenta',
            'margeB',
            'margeD',
            'margeG',
            'margeH',
            'maroon',
            'mediumaquamarine',
            'mediumblue',
            'mediumorchid',
            'mediumpurple',
            'mediumseagreen',
            'mediumslateblue',
            'mediumspringgreen',
            'mediumturquoise',
            'mediumvioletred',
            'midnightblue',
            'mintcream',
            'mistyrose',
            'miter',
            'moccasin',
            'move',
            'navajowhite',
            'navy',
            'noline',
            'none',
            'normalsize',
            'obj',
            'oldlace',
            'olive',
            'olivedrab',
            'oplus',
            'orange',
            'orangered',
            'orchid',
            'ortho',
            'otimes',
            'palegoldenrod',
            'palegreen',
            'paleturquoise',
            'palevioletred',
            'papayawhip',
            'pdf',
            'pdfc',
            'peachpuff',
            'pentagon',
            'peru',
            'pgf',
            'pi',
            'pink',
            'plum',
            'plus',
            'powderblue',
            'psf',
            'pst',
            'purple',
            'red',
            'right',
            'rosybrown',
            'round',
            'royalblue',
            'saddlebrown',
            'salmon',
            'sandybrown',
            'scriptsize',
            'seagreen',
            'seashell',
            'sep',
            'sep3D',
            'sienna',
            'silver',
            'skyblue',
            'slateblue',
            'slategray',
            'small',
            'snow',
            'solid',
            'special',
            'springgreen',
            'square',
            'src4latex',
            'stacked',
            'steelblue',
            'svg',
            'teal',
            'teg',
            'tex',
            'texsrc',
            'thicklines',
            'thinlines',
            'thistle',
            'times',
            'tiny',
            'tkz',
            'tomato',
            'top',
            'triangle',
            'turquoise',
            'user',
            'userdash',
            'version',
            'vertical',
            'violet',
            'wheat',
            'white',
            'whitesmoke',
            'yellow',
            'yellowgreen',
        ),
        6 => array( //macros prédéfinies
            'Abs',
            'Anp',
            'Apercu',
            'Arc',
            'Arc3D',
            'AretesNum',
            'AxeX3D',
            'AxeY3D',
            'AxeZ3D',
            'Axes3D',
            'Bcolor',
            'Bouton',
            'BoxAxes3D',
            'BrightColor',
            'Bsave',
            'Ceil',
            'Cercle',
            'Cercle3D',
            'Chanfrein',
            'ChangeWinTo',
            'Clip',
            'Clip3D',
            'ColorJump',
            'CompVer',
            'CompileEps',
            'CompilePdf',
            'Cone',
            'Courbe3D',
            'CpCopy',
            'CpDel',
            'CpNops',
            'CpReplace',
            'CpReverse',
            'CplColor',
            'Cvx2d',
            'Cvx3d',
            'Cvx3dAux',
            'Cylindre',
            'Dark',
            'Dbissec',
            'Dcarre',
            'Dcone',
            'Dcylindre',
            'Ddroite',
            'Dmed',
            'Dparallel',
            'Dparallelep',
            'Dparallelo',
            'Dperp',
            'Dpolyreg',
            'DpqGoneReg',
            'DpqGoneReg3D',
            'Dprisme',
            'Dpyramide',
            'DrawAretes',
            'DrawDdroite',
            'DrawDot',
            'DrawDroite',
            'DrawFaces',
            'DrawFacet',
            'DrawFlatFacet',
            'DrawGouraudTr',
            'DrawPlan',
            'DrawPoly',
            'DrawPolyNC',
            'DrawSmoothFacet',
            'Drectangle',
            'Dsphere',
            'Dsurface',
            'Dtetraedre',
            'Esave',
            'ExportGouraudTr',
            'ExportSmoothFacet',
            'FacesNum',
            'Gcolor',
            'GradDroite',
            'HollowFacet',
            'Hsb',
            'HueColor',
            'Incfrac',
            'Intersec',
            'Intersection',
            'IsAlign',
            'IsAlign3d',
            'IsIn',
            'IsPlan',
            'KillDup',
            'KillDup3D',
            'LabelArc',
            'LabelAxe',
            'LabelDot',
            'LabelDot3D',
            'LabelSeg',
            'Lcolor',
            'Light',
            'Ligne3D',
            'MakeVer',
            'Map3D',
            'MapBy',
            'Merge3d',
            'MixColor',
            'MouseZoom',
            'NewLabel',
            'NewLabelDot',
            'NewLabelDot3D',
            'NewTeXlabel',
            'Nops3d',
            'Ordonner',
            'Palette',
            'Parallelep',
            'Point3D',
            'Pos',
            'Pos3d',
            'Prisme',
            'Pyramide',
            'Rarc',
            'Rcercle',
            'Rcolor',
            'RealArg',
            'RealCoord',
            'RealCoordV',
            'Rellipse',
            'RellipticArc',
            'RestoreTphi',
            'RestoreWin',
            'RestoreWin3d',
            'Rgb2Gray',
            'Rgb2Hexa',
            'Rgb2Hsb',
            'RgbL',
            'Ryb',
            'SatColor',
            'SaveTphi',
            'SaveWin',
            'SaveWin3d',
            'SceneToGeom',
            'SceneToJvx',
            'SceneToObj',
            'ScrCoord',
            'ScrCoordV',
            'ScreenCenter',
            'ScreenPos',
            'ScreenX',
            'ScreenY',
            'Section',
            'Section2',
            'Seg',
            'SetStr',
            'Snapshot',
            'SortWith',
            'Sphere',
            'StrListAdd',
            'StrListCopy',
            'StrListDelKey',
            'StrListDelVal',
            'StrListGetKey',
            'StrListInit',
            'StrListInsert',
            'StrListKill',
            'StrListReplace',
            'StrListReplaceKey',
            'StrListShow',
            'StrNum',
            'SvgCoord',
            'TeXCoord',
            'Tetra',
            'VarGlob',
            'WriteObj',
            'WriteOff',
            'Xde',
            'Yde',
            'Zde',
            'addfrac',
            'affin',
            'aire3d',
            'angle',
            'angle3d',
            'angleD',
            'antirot3d',
            'arc',
            'arcBezier',
            'axeX',
            'axeY',
            'axes',
            'background',
            'bande',
            'bary',
            'bary3d',
            'bbox',
            'bdAngleD',
            'bdArc',
            'bdAxes',
            'bdCercle',
            'bdCone',
            'bdCurve',
            'bdCylinder',
            'bdDot',
            'bdDroite',
            'bdFacet',
            'bdLabel',
            'bdLine',
            'bdPlan',
            'bdPlanEqn',
            'bdPrism',
            'bdPyramid',
            'bdSphere',
            'bdSurf',
            'bdTorus',
            'bdWall',
            'binom',
            'bissec',
            'bordsAjour',
            'cap',
            'capB',
            'carre',
            'centerView',
            'chaine',
            'class_Path',
            'clipCurve',
            'clipPoly',
            'compileFormule',
            'conv2Facet',
            'conv2FlatPs',
            'coord',
            'cup',
            'cupB',
            'curve2Cone',
            'curve2Cylinder',
            'curveTube',
            'cutBezier',
            'defAff',
            'defAff3d',
            'del',
            'det',
            'det3d',
            'div',
            'domaine1',
            'domaine2',
            'domaine3',
            'dproj3d',
            'dproj3dO',
            'drawFlatPs',
            'drawSet',
            'drawTeXlabel',
            'drawTeXlabel3d',
            'drawWin3d',
            'dsym3d',
            'dsym3dO',
            'ecart',
            'ellipseArc',
            'engineerF',
            'epsCoord',
            'extractFlatPs',
            'fact',
            'flecher',
            'free',
            'ftransform',
            'ftransform3d',
            'geomview',
            'getdot',
            'getdroite',
            'getplan',
            'getplanEqn',
            'grille3d',
            'help',
            'hom',
            'hom3d',
            'interDD',
            'interDP',
            'interLP',
            'interPP',
            'inv',
            'inv3d',
            'invmatrix',
            'invmatrix3d',
            'isobar',
            'isobar3d',
            'javaview',
            'label',
            'labelarc',
            'length',
            'length3d',
            'line2Cone',
            'line2Cylinder',
            'lineTube',
            'loadFlatPs',
            'makeLabel3d',
            'markangle',
            'markseg',
            'markseg3d',
            'matrix',
            'matrix3d',
            'max',
            'med',
            'median',
            'min',
            'mod',
            'moy',
            'mtransform',
            'mtransform3d',
            'mulmatrix',
            'mulmatrix3d',
            'n',
            'newxlegend',
            'newylegend',
            'newzlegend',
            'nil',
            'normalize',
            'not',
            'parallel',
            'parallelo',
            'pdfprog',
            'periodic',
            'permute',
            'permute3d',
            'perp',
            'pgcd',
            'planEqn',
            'polyreg',
            'ppcm',
            'pqGoneReg',
            'pqGoneReg3D',
            'prod',
            'proj',
            'proj3d',
            'proj3dO',
            'projO',
            'purge3d',
            'putAbove',
            'px',
            'pxy',
            'pxz',
            'py',
            'pyz',
            'pz',
            'rect',
            'rectangle',
            'rectangle3d',
            'replace',
            'replace3d',
            'reverse',
            'reverse3d',
            'rot',
            'rot3d',
            'rotCurve',
            'rotLine',
            'set',
            'setB',
            'setminus',
            'setminusB',
            'shift',
            'shift3d',
            'simil',
            'size',
            'split2facet1',
            'split2facet2',
            'suite',
            'sum',
            'svgCoord',
            'sym',
            'sym3d',
            'sym3dO',
            'symG',
            'symO',
            'tangente',
            'tangenteP',
            'texCoord',
            'transformbox3d',
            'trianguler',
            'var',
            'view',
            'view3D',
            'viewDir',
            'visible',
            'wedge',
            'zoom',
        ),
        7 => array( //variables prédéfinies
            'AngleStep',
            'Arrows',
            'AutoReCalc',
            'Color',
            'ComptGraph',
            'ComptLabel3d',
            'DashPattern',
            'DeltaB',
            'DotAngle',
            'DotScale',
            'DotSize',
            'DotStyle',
            'Eofill',
            'FillColor',
            'FillOpacity',
            'FillStyle',
            'ForMinToMax',
            'HideColor',
            'HideStyle',
            'HideWidth',
            'IsVisible',
            'LabelAngle',
            'LabelSize',
            'LabelStyle',
            'LineCap',
            'LineJoin',
            'LineStyle',
            'MiterLimit',
            'MouseCode',
            'NbBoutons',
            'NbPoints',
            'Origin',
            'PenMode',
            'RefPoint',
            'ScriptExt',
            'StrokeOpacity',
            'TeXLabel',
            'TeXify',
            'TeXifyLabels',
            'TphiList',
            'Width',
            'Xfact',
            'Xinf',
            'Xsup',
            'Yfact',
            'Yinf',
            'Ysup',
            'Zinf',
            'ZoomList',
            'Zsup',
            'above',
            'arrows',
            'arrowscale',
            'axeOrigin',
            'backcolor',
            'backculling',
            'border',
            'bordercolor',
            'cleanLabel',
            'clip',
            'clipwin',
            'close',
            'color',
            'contrast',
            'cube',
            'defaultMatrix',
            'deg',
            'dir',
            'disc',
            'dollar',
            'dotcolor',
            'dotscale',
            'dotstyle',
            'drawbox',
            'flip',
            'grid',
            'gridcolor',
            'gridwidth',
            'height',
            'hidden',
            'hiddenLines',
            'hollow',
            'inside',
            'label3d',
            'labeldir',
            'labelpos',
            'labels',
            'labelsep',
            'labelsize',
            'labelstyle',
            'legendpos',
            'linestyle',
            'maxGrad',
            'mirror',
            'mm',
            'nbdeci',
            'nbdot',
            'nbfacet',
            'normal',
            'numericFormat',
            'opacity',
            'originlabel',
            'outside',
            'phi',
            'position',
            'rad',
            'radius',
            'radiusscale',
            'radscale',
            'rotation',
            'scale',
            'select',
            'showdot',
            'smooth',
            'stock',
            'stock1',
            'stock2',
            'stock3',
            'stock4',
            'stock5',
            't',
            'tMax',
            'tMin',
            'tailleB',
            'theta',
            'tickdir',
            'tickpos',
            'tube',
            'twoside',
            'u',
            'usecomma',
            'v',
            'vecI',
            'vecJ',
            'vecK',
            'width',
            'win2dList',
            'win3dList',
            'xaxe',
            'xgradlimits',
            'xlabelsep',
            'xlabelstyle',
            'xlegendsep',
            'xlimits',
            'xstep',
            'xylabelpos',
            'xylabelsep',
            'xyticks',
            'yaxe',
            'ygradlimits',
            'ylabelsep',
            'ylabelstyle',
            'ylegendsep',
            'ylimits',
            'ystep',
            'zaxe',
            'zgradlimits',
            'zlabelsep',
            'zlabelstyle',
            'zlegendsep',
            'zlimits',
            'zstep',
        )
    ),
    'SYMBOLS' => array(
        ':=', '=', '+', '-', '*', '/',
        '<', '>', '>=', '<=', '<>',
        '\\', '@', ', ', ';', '#'
    ),
    'CASE_SENSITIVE' => array(
        GESHI_COMMENTS => false,
        1 => true,
        2 => true,
        3 => true,
        4 => true,
        5 => true,
        6 => true,
        7 => true
    ),
    'STYLES' => array(
        'KEYWORDS' => array(
            1  => 'color: #3ad900;font-weight: bold;',// file construction
            2  => 'color: #A53;',// programmation structure
            3  => 'color: #35A;font-weight: bold;',// commandes prédéfinies
            4  => 'color: #472;',// commandes relatives à l'interface graphique
            5  => 'color: #008080;',// constantes prédéfinies
            6  => 'color: #808000;font-weight: bold;',// macros prédéfinies
            7  => 'color: #000;font-weight: bold;',// variables prédéfinies
        ),
        'COMMENTS' => array(
            1 => 'color: #777;',
            'MULTI' => 'color: #880;'
        ),
        'ESCAPE_CHAR' => array(
            0 => ''
        ),
        'BRACKETS' => array(
            0 => 'color: #820;'
        ),
        'STRINGS' => array(
            0 => 'color: #880;'
        ),
        'NUMBERS' => array(
            0 => 'color: #000;'
        ),
        'METHODS' => array(
            1 => '',
            2 => ''
        ),
        'SYMBOLS' => array(
            0 => 'color: #000;'
        ),
        'REGEXPS' => array(
        ),
        'SCRIPT' => array(
            0 => ''
        )
    ),
    'URLS' => array(
        1 => '',
        2 => '',
        3 => 'http://melusine.eu.org/syracuse/G/geshi/docs/texgraph/#{FNAME}',
        4 => '',
        5 => '',
        6 => 'http://melusine.eu.org/syracuse/G/geshi/docs/texgraph/#{FNAME}',
        7 => ''
    ),
    'OOLANG' => false,
    'OBJECT_SPLITTERS' => array(
    ),
    'REGEXPS' => array(
    ),
    'STRICT_MODE_APPLIES' => GESHI_NEVER,
    'SCRIPT_DELIMITERS' => array(
    ),
    'HIGHLIGHT_STRICT_BLOCK' => array(
    )
);
