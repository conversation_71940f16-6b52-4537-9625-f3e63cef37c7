<?php
/**
 * Unified Bootstrap for Laravel + ImpressCMS Integration
 * This file replaces the ImpressCMS autoloader with <PERSON>'s autoloader
 * while maintaining backward compatibility with existing ImpressCMS functionality
 *
 * @copyright The ImpressCMS Project
 * @license GPL
 */

// Prevent direct access
defined("ICMS_ROOT_PATH") or die("ImpressCMS root path not defined");

// Define essential constants if not already defined
if (!defined('ICMS_URL')) {
    // Try to determine the URL from the current request
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
    $basePath = dirname(dirname($scriptName)); // Remove /legacy/include from path
    define('ICMS_URL', $protocol . $host . $basePath);
}

if (!defined('ICMS_THEME_URL')) {
    define('ICMS_THEME_URL', ICMS_URL . '/themes');
}

if (!defined('ICMS_THEME_PATH')) {
    define('ICMS_THEME_PATH', ICMS_ROOT_PATH . '/themes');
}

if (!defined('ICMS_UPLOAD_URL')) {
    define('ICMS_UPLOAD_URL', ICMS_URL . '/uploads');
}

if (!defined('ICMS_UPLOAD_PATH')) {
    define('ICMS_UPLOAD_PATH', ICMS_ROOT_PATH . '/uploads');
}

// Load Composer autoloader
$composerAutoloadPath = ICMS_ROOT_PATH . '/../vendor/autoload.php';
if (!file_exists($composerAutoloadPath)) {
    // Try alternative path for different directory structures
    $composerAutoloadPath = ICMS_ROOT_PATH . '/vendor/autoload.php';
    if (!file_exists($composerAutoloadPath)) {
        // Try one more alternative path
        $composerAutoloadPath = dirname(ICMS_ROOT_PATH) . '/vendor/autoload.php';
        if (!file_exists($composerAutoloadPath)) {
            die("Composer autoloader not found. Please run 'composer install' first.");
        }
    }
}

// Load Composer autoloader
require_once $composerAutoloadPath;

/**
 * Legacy compatibility layer for ImpressCMS autoloader
 * This ensures that existing code that expects the ImpressCMS autoloader continues to work
 */
class icms_ComposerAutoloaderBridge {

    /**
     * Register the legacy compatibility functions
     */
    public static function register() {
        // Register a fallback autoloader for any classes that Composer can't find
        spl_autoload_register([__CLASS__, 'legacyAutoload'], true, false);
    }

    /**
     * Legacy autoloader fallback
     * This handles any classes that weren't found by Composer's autoloader
     */
    public static function legacyAutoload($className) {
        // Handle ICMS classes that follow the old naming convention
        if (strpos($className, 'icms_') === 0) {
            $classPath = self::convertClassNameToPath($className);
            if ($classPath && file_exists($classPath)) {
                require_once $classPath;
                return true;
            }
        }

        // Handle XOOPS legacy classes
        if (strpos($className, 'Xoops') === 0 || strpos($className, 'xoops') === 0) {
            $classPath = self::convertXoopsClassNameToPath($className);
            if ($classPath && file_exists($classPath)) {
                require_once $classPath;
                return true;
            }
        }

        // Handle module-specific classes
        if (strpos($className, 'System') === 0) {
            $classPath = self::convertSystemClassNameToPath($className);
            if ($classPath && file_exists($classPath)) {
                require_once $classPath;
                return true;
            }
        }

        return false;
    }

    /**
     * Convert ICMS class name to file path
     */
    private static function convertClassNameToPath($className) {
        // Remove 'icms_' prefix
        $classPath = substr($className, 5);

        // Convert underscores to directory separators
        $classPath = str_replace('_', '/', $classPath);

        // Build full path
        $fullPath = ICMS_LIBRARIES_PATH . '/icms/' . $classPath . '.php';

        return $fullPath;
    }

    /**
     * Convert XOOPS class name to file path
     */
    private static function convertXoopsClassNameToPath($className) {
        // Handle XOOPS legacy classes - these might be in various locations
        $possiblePaths = [
            ICMS_ROOT_PATH . '/class/' . strtolower($className) . '.php',
            ICMS_ROOT_PATH . '/include/' . strtolower($className) . '.php',
            ICMS_LIBRARIES_PATH . '/' . strtolower($className) . '.php'
        ];

        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }

        return false;
    }

    /**
     * Convert System module class name to file path
     */
    private static function convertSystemClassNameToPath($className) {
        // Handle system module classes
        $classFile = strtolower($className) . '.php';

        $possiblePaths = [
            ICMS_ROOT_PATH . '/modules/system/class/' . $classFile,
            ICMS_ROOT_PATH . '/modules/system/admin/' . $classFile,
        ];

        // Check subdirectories in admin
        $adminDirs = ['adsense', 'autotasks', 'blocksadmin', 'blockspadmin', 'customtag',
                      'mimetype', 'pages', 'rating', 'userrank', 'modulesadmin'];

        foreach ($adminDirs as $dir) {
            $possiblePaths[] = ICMS_ROOT_PATH . '/modules/system/admin/' . $dir . '/class/' . $classFile;
        }

        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }

        return false;
    }
}

/**
 * Replacement for the original icms::setup() method
 * This maintains the same interface but uses Composer autoloader
 */
class icms {

    public static $module;
    public static $user;
    public static $config;
    public static $security;
    public static $logger;
    public static $session;
    public static $xoopsDB;

    private static $handlers = [];
    private static $services = [];
    private static $isSetup = false;

    /**
     * Setup method - replaces the original ImpressCMS setup
     */
    public static function setup() {
        if (self::$isSetup) {
            return;
        }

        // Register the legacy compatibility autoloader
        icms_ComposerAutoloaderBridge::register();

        // Initialize core objects (maintaining backward compatibility)
        self::initializeCoreObjects();

        self::$isSetup = true;
    }

    /**
     * Boot method - maintains compatibility with original boot process
     */
    public static function boot() {
        // This method maintains the same interface as the original
        // but the actual bootstrapping is now handled by Composer

        // Initialize any remaining legacy components
        self::initializeLegacyComponents();
    }

    /**
     * Handler method - maintains compatibility with icms::handler()
     */
    public static function &handler($name, $optional = false) {
        if (!isset(self::$handlers[$name])) {
            // Initialize database connection if not already done
            if (!self::$xoopsDB) {
                self::initializeDatabase();
            }

            $class = $name . "Handler";
            if (!class_exists($class)) {
                $class = $name . "_Handler";
                if (!class_exists($class)) {
                    // Try old style handler loading
                    $lower = strtolower(trim($name));
                    if (file_exists($hnd_file = ICMS_ROOT_PATH.'/kernel/' . $lower . '.php')) {
                        require_once $hnd_file;
                    } elseif (file_exists($hnd_file = ICMS_ROOT_PATH.'/class/' . $lower . '.php')) {
                        require_once $hnd_file;
                    }
                    if (!class_exists($class = 'Xoops' . ucfirst($lower) . 'Handler', false)) {
                        if (!class_exists($class = 'Icms' . ucfirst($lower) . 'Handler', false)) {
                            // Not found at all
                            $class = false;
                        }
                    }
                }
            }

            self::$handlers[$name] = $class ? new $class(self::$xoopsDB) : false;
        }

        if (!self::$handlers[$name] && !$optional) {
            throw new RuntimeException(sprintf("Handler <b>%s</b> does not exist", $name));
        }

        return self::$handlers[$name];
    }

    /**
     * Service method - maintains compatibility with icms::loadService()
     */
    public static function loadService($name, $args = null, $options = null) {
        // Maintain compatibility with the original service loading
        if (!isset(self::$services[$name])) {
            // Load service using the new autoloader
            $serviceClass = self::getServiceClass($name);
            if (class_exists($serviceClass)) {
                self::$services[$name] = new $serviceClass($args, $options);
            }
        }

        return self::$services[$name] ?? false;
    }

    /**
     * Launch module method - maintains compatibility with icms::launchModule()
     * This method registers module class paths and loads the module service
     */
    public static function launchModule() {
        // For now, this method exists for compatibility but doesn't do the full initialization
        // This prevents the "Call to undefined method" error
        // In a full implementation, this would require proper database setup

        try {
            // Only attempt module loading if we have a proper database connection
            if (self::$xoopsDB && isset(self::$xoopsDB->connected) && self::$xoopsDB->connected) {
                $module_handler = self::handler("icms_module", true); // Optional handler
                if ($module_handler) {
                    $modules = $module_handler->getObjects();
                    if (is_array($modules)) {
                        foreach ($modules as $module) {
                            if (method_exists($module, 'registerClassPath')) {
                                $module->registerClassPath(true);
                            }
                        }
                    }
                }

                $isAdmin = (defined('ICMS_IN_ADMIN') && (int)ICMS_IN_ADMIN);
                self::loadService('module', array('icms_module_Handler', 'service'), array($isAdmin));
            }

        } catch (Exception $e) {
            // Silently handle errors to maintain compatibility
            // This allows the system to continue running even if module loading fails
            if (defined('ICMS_DEBUG') && ICMS_DEBUG) {
                error_log("icms::launchModule() error: " . $e->getMessage());
            }
        }
    }

    /**
     * Shutdown method - maintains compatibility with icms::shutdown()
     * Finalizes all processes as the script exits
     */
    public static function shutdown() {
        try {
            // Ensure the session service can write data before the DB connection is closed
            if (session_id()) {
                session_write_close();
            }

            // Ensure the logger can decorate output before objects are destroyed
            while (@ob_end_flush());

        } catch (Exception $e) {
            // Silently handle errors during shutdown
            error_log("icms::shutdown() error: " . $e->getMessage());
        }
    }

    /**
     * Initialize core objects for backward compatibility
     */
    private static function initializeCoreObjects() {
        // These will be initialized as needed, maintaining lazy loading
        // but ensuring compatibility with existing code
    }

    /**
     * Initialize legacy components
     */
    private static function initializeLegacyComponents() {
        // Initialize any legacy components that are still needed
        // This maintains backward compatibility
    }

    /**
     * Initialize database connection
     */
    private static function initializeDatabase() {
        if (!self::$xoopsDB) {
            // Try to initialize database connection
            // This is a simplified version - in a full implementation,
            // you would need to load the database configuration
            try {
                // For now, create a null database object to prevent errors
                // In a full implementation, this would connect to the actual database
                self::$xoopsDB = new stdClass();
                self::$xoopsDB->connected = false;
            } catch (Exception $e) {
                self::$xoopsDB = null;
            }
        }
    }

    /**
     * Get handler class name
     */
    private static function getHandlerClass($name) {
        // Convert handler name to class name
        $className = 'icms_' . str_replace('_', '_', $name) . '_Handler';

        // Handle special cases
        $specialCases = [
            'icms_module' => 'icms_module_Handler',
            'icms_user' => 'icms_member_user_Handler',
            'icms_group' => 'icms_member_group_Handler',
            'icms_data_avatar' => 'icms_data_avatar_Handler',
            'icms_data_file' => 'icms_data_file_Handler',
        ];

        return $specialCases[$name] ?? $className;
    }

    /**
     * Get service class name
     */
    private static function getServiceClass($name) {
        return 'icms_' . str_replace('_', '_', $name);
    }
}

// Register the unified autoloader
icms_ComposerAutoloaderBridge::register();
?>
