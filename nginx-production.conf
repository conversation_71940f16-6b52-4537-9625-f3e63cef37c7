server {
    # <PERSON><PERSON> Herd handles port assignment automatically
    # Remove explicit listen directives to let <PERSON><PERSON> manage ports
    server_name imlara.test;

    # Windows-style path - Her<PERSON> typically uses forward slashes even on Windows
    # Update this to your actual Windows project path
    root C:/Users/<USER>/sites/imlara;

    # Default index files
    index index.php index.html index.htm;

    # Laravel Herd manages logging automatically
    # These paths are optional as Her<PERSON> has its own logging system
    access_log off;  # Let Herd handle logging
    error_log off;   # Let Herd handle logging

    # Security headers - compatible with Herd
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: ws: wss: data: blob: 'unsafe-inline' 'unsafe-eval'" always;

    # Deny access to sensitive files
    location ~ /\.(?!well-known).* {
        deny all;
    }

    location ~ \.(log|env)$ {
        deny all;
    }

    # Prevent direct access to legacy directory
    location ^~ /legacy/ {
        deny all;
    }

    # Handle static assets - try legacy first, then public
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|pdf|txt|xml)$ {
        try_files /legacy$uri /public$uri =404;
        expires 1M;
        add_header Cache-Control "public, immutable";
    }

    # Special handling for PHP files that exist in legacy directory
    location ~ \.php$ {
        # Check if the file exists in legacy directory
        set $legacy_file $document_root/legacy$uri;

        # If legacy file exists, process it with proper context
        if (-f $legacy_file) {
            # Laravel Herd uses TCP connection for PHP-FPM on Windows
            # Herd typically uses port 9000 or dynamically assigned ports
            fastcgi_pass 127.0.0.1:9000;
            fastcgi_index index.php;

            # Critical: Set the script filename to the legacy file (Windows path format)
            fastcgi_param SCRIPT_FILENAME $legacy_file;
            fastcgi_param SCRIPT_NAME $uri;
            fastcgi_param REQUEST_URI $request_uri;
            fastcgi_param DOCUMENT_ROOT $document_root/legacy;
            fastcgi_param PHP_SELF $uri;

            # Set working directory to legacy for proper includes (Windows format)
            fastcgi_param PWD $document_root/legacy;

            # Windows-specific environment variables for Herd
            fastcgi_param SYSTEMROOT $document_root/legacy;
            fastcgi_param PATH_INFO $fastcgi_path_info;
            fastcgi_param PATH_TRANSLATED $document_root$fastcgi_path_info;

            # Include standard fastcgi params (Herd compatible)
            include fastcgi_params;

            # Override some critical params for legacy context
            fastcgi_param ORIG_SCRIPT_FILENAME $legacy_file;
            fastcgi_param CONTEXT "legacy";
            fastcgi_param SERVER_SOFTWARE "nginx/herd";

            # Herd-specific timeout settings
            fastcgi_read_timeout 300;
            fastcgi_send_timeout 300;

            break;
        }

        # If no legacy file, route to Laravel
        try_files /public$uri /public/index.php?$query_string;
    }

    # Main location - handle all other requests
    location / {
        # Try legacy directory first, then public, then Laravel
        try_files /legacy$uri /public$uri /public/index.php?$query_string;
    }

    # Handle PHP files in public directory (Laravel) - Herd compatible
    location ~ ^/public/(.+\.php)$ {
        # Laravel Herd TCP connection
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;

        # Windows-compatible paths
        fastcgi_param SCRIPT_FILENAME $document_root/public/$1;
        fastcgi_param SCRIPT_NAME /$1;
        fastcgi_param DOCUMENT_ROOT $document_root/public;
        fastcgi_param REQUEST_URI $request_uri;
        fastcgi_param QUERY_STRING $query_string;

        # Herd-specific parameters
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_param PATH_TRANSLATED $document_root/public$fastcgi_path_info;
        fastcgi_param SERVER_SOFTWARE "nginx/herd";

        # Herd timeout settings
        fastcgi_read_timeout 300;
        fastcgi_send_timeout 300;

        include fastcgi_params;
    }

    # Remove trailing slashes
    location ~ ^(.+)/$ {
        return 301 $1;
    }

    # Error pages
    error_page 404 /public/index.php;
    error_page 500 502 503 504 /public/index.php;
}
