server {
    listen 80;
    server_name imlara.test;
    
    # Document root points to the Laravel project root
    root /path/to/your/imlara/project;
    
    # Default index files
    index index.php index.html index.htm;
    
    # Logging
    access_log /var/log/nginx/imlara.test.access.log;
    error_log /var/log/nginx/imlara.test.error.log;
    
    # Security: Deny access to sensitive files
    location ~ /\.(?!well-known).* {
        deny all;
    }
    
    location ~ \.(log|env)$ {
        deny all;
    }
    
    # Handle static assets - try legacy first, then public
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|pdf|txt|xml)$ {
        try_files /legacy$uri /public$uri =404;
        expires 1M;
        add_header Cache-Control "public, immutable";
    }
    
    # Route everything through Laravel - let the middleware handle dual routing
    location / {
        try_files /public$uri /public/index.php?$query_string;
    }
    
    # Handle PHP files in public directory (Laravel)
    location ~ ^/public/(.+\.php)$ {
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;  # Adjust PHP version as needed
        fastcgi_index index.php;
        
        fastcgi_param SCRIPT_FILENAME $document_root/public/$1;
        fastcgi_param SCRIPT_NAME /$1;
        fastcgi_param DOCUMENT_ROOT $document_root/public;
        fastcgi_param REQUEST_URI $request_uri;
        fastcgi_param QUERY_STRING $query_string;
        
        include fastcgi_params;
    }
    
    # Remove trailing slashes
    location ~ ^(.+)/$ {
        return 301 $1;
    }
}
