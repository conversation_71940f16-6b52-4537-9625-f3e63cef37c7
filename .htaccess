#<IfModule mod_rewrite.c>
#    <IfModule mod_negotiation.c>
#        Options -MultiViews -Indexes
#    </IfModule>
#
#    RewriteEngine On
#
#    # Handle Authorization Header
#    RewriteCond %{HTTP:Authorization} .
#    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
#
#    # Redirect Trailing Slashes If Not A Folder...
#    RewriteCond %{REQUEST_FILENAME} !-d
#    RewriteCond %{REQUEST_URI} (.+)/$
#    RewriteRule ^ %1 [L,R=301]
#
#    # Send Requests To Front Controller...
#    RewriteCond %{REQUEST_FILENAME} !-d
#    RewriteCond %{REQUEST_FILENAME} !-f
#    RewriteRule ^ /public/index.php [L]
#</IfModule>
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /

    # Don’t rewrite requests already targeting /public/
    RewriteCond %{REQUEST_URI} !^/public/

    # If the file or directory doesn’t exist, send to public/index.php
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ /public/index.php [L,QSA]
</IfModule>
