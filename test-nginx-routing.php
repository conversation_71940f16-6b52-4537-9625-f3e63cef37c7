<?php
/**
 * Test script to verify nginx dual routing functionality
 * Run this from command line: php test-nginx-routing.php
 */

echo "Testing nginx dual routing for imlara.test\n";
echo "==========================================\n\n";

$baseUrl = 'http://imlara.test';
$tests = [
    // Test 1: Root should redirect to install
    [
        'url' => $baseUrl,
        'description' => 'Root URL should redirect to ImpressCMS installation',
        'expected' => 'redirect or installation wizard'
    ],
    
    // Test 2: Legacy file routing
    [
        'url' => $baseUrl . '/user.php',
        'description' => 'Legacy user.php should be served from /legacy/user.php',
        'expected' => 'ImpressCMS installation wizard or user page'
    ],
    
    // Test 3: Legacy test file
    [
        'url' => $baseUrl . '/test-legacy.php',
        'description' => 'Custom legacy test file should work',
        'expected' => 'JSON response with legacy system info'
    ],
    
    // Test 4: Laravel routing
    [
        'url' => $baseUrl . '/laravel-test',
        'description' => 'Laravel test route should work',
        'expected' => 'JSON response with Laravel system info'
    ],
    
    // Test 5: Non-existent file (should fall back to Laravel)
    [
        'url' => $baseUrl . '/non-existent-file',
        'description' => 'Non-existent file should fall back to Laravel 404',
        'expected' => 'Laravel 404 response'
    ],
    
    // Test 6: Install directory
    [
        'url' => $baseUrl . '/install/index.php',
        'description' => 'Install directory should work',
        'expected' => 'ImpressCMS installation wizard'
    ]
];

foreach ($tests as $i => $test) {
    echo "Test " . ($i + 1) . ": " . $test['description'] . "\n";
    echo "URL: " . $test['url'] . "\n";
    echo "Expected: " . $test['expected'] . "\n";
    
    // Initialize cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $test['url']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false); // Don't follow redirects automatically
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Nginx Dual Routing Test Script');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ ERROR: " . $error . "\n";
    } else {
        // Parse response
        $headers = substr($response, 0, $headerSize);
        $body = substr($response, $headerSize);
        
        echo "Status: " . $httpCode . "\n";
        
        // Check for redirects
        if ($httpCode >= 300 && $httpCode < 400) {
            preg_match('/Location: (.+)/', $headers, $matches);
            $location = isset($matches[1]) ? trim($matches[1]) : 'Unknown';
            echo "✅ REDIRECT to: " . $location . "\n";
        } elseif ($httpCode == 200) {
            // Check content type and first few characters
            preg_match('/Content-Type: (.+)/', $headers, $matches);
            $contentType = isset($matches[1]) ? trim($matches[1]) : 'Unknown';
            
            $preview = substr(strip_tags($body), 0, 100);
            echo "✅ SUCCESS (200 OK)\n";
            echo "Content-Type: " . $contentType . "\n";
            echo "Preview: " . $preview . "...\n";
        } else {
            echo "⚠️  HTTP " . $httpCode . "\n";
            $preview = substr(strip_tags($body), 0, 100);
            echo "Preview: " . $preview . "...\n";
        }
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
}

echo "Test completed!\n\n";
?>
