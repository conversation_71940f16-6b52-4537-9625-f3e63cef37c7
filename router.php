<?php
/**
 * Enhanced router for dual routing system with full HTTP method and MIME type support
 * Supports GET, POST, PUT, DELETE, PATCH and all other HTTP methods for legacy PHP files
 * Provides correct MIME type detection for static files
 */

/**
 * Get proper MIME type based on file extension
 */
function getMimeType($filePath) {
    $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

    $mimeTypes = [
        // Web files
        'html' => 'text/html',
        'htm' => 'text/html',
        'php' => 'text/html',
        'css' => 'text/css',
        'js' => 'application/javascript',
        'json' => 'application/json',
        'xml' => 'application/xml',
        'txt' => 'text/plain',

        // Images
        'png' => 'image/png',
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'gif' => 'image/gif',
        'bmp' => 'image/bmp',
        'ico' => 'image/x-icon',
        'svg' => 'image/svg+xml',
        'webp' => 'image/webp',

        // Fonts
        'woff' => 'font/woff',
        'woff2' => 'font/woff2',
        'ttf' => 'font/ttf',
        'eot' => 'application/vnd.ms-fontobject',
        'otf' => 'font/otf',

        // Documents
        'pdf' => 'application/pdf',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls' => 'application/vnd.ms-excel',
        'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',

        // Archives
        'zip' => 'application/zip',
        'rar' => 'application/x-rar-compressed',
        'tar' => 'application/x-tar',
        'gz' => 'application/gzip',

        // Audio/Video
        'mp3' => 'audio/mpeg',
        'wav' => 'audio/wav',
        'mp4' => 'video/mp4',
        'avi' => 'video/x-msvideo',
        'mov' => 'video/quicktime',

        // Other
        'swf' => 'application/x-shockwave-flash',
        'map' => 'application/json', // Source maps
    ];

    if (isset($mimeTypes[$extension])) {
        return $mimeTypes[$extension];
    }

    // Fallback to mime_content_type if available
    if (function_exists('mime_content_type') && file_exists($filePath)) {
        $detectedType = mime_content_type($filePath);
        if ($detectedType !== false) {
            return $detectedType;
        }
    }

    // Final fallback
    return 'application/octet-stream';
}

// Get the requested URI
$requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Remove leading slash
$requestUri = ltrim($requestUri, '/');

// Handle directory requests (add index.php if needed)
$legacyPath = __DIR__ . '/legacy/' . $requestUri;

// If it's a directory, try to find index.php
if (is_dir($legacyPath)) {
    $indexPath = rtrim($legacyPath, '/') . '/index.php';
    if (file_exists($indexPath)) {
        $legacyPath = $indexPath;
        $requestUri = rtrim($requestUri, '/') . '/index.php';
    }
}

// Check if the legacy file exists
if (file_exists($legacyPath) && is_file($legacyPath)) {
    $extension = strtolower(pathinfo($legacyPath, PATHINFO_EXTENSION));

    if ($extension === 'php') {
        // Handle PHP files with full HTTP method support
        serveLegacyPhpFile($legacyPath, $requestUri);
    } else {
        // Handle static files with proper MIME types
        serveStaticFile($legacyPath);
    }
}

/**
 * Serve legacy PHP files with proper environment setup and HTTP method support
 */
function serveLegacyPhpFile($legacyPath, $requestUri) {
    // Store original environment
    $originalEnv = [
        'cwd' => getcwd(),
        'script_name' => $_SERVER['SCRIPT_NAME'] ?? '',
        'script_filename' => $_SERVER['SCRIPT_FILENAME'] ?? '',
        'php_self' => $_SERVER['PHP_SELF'] ?? '',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? '',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
    ];

    try {
        // Set up legacy execution environment
        $legacyFileDir = dirname($legacyPath);
        $legacyRoot = __DIR__ . '/legacy';

        // Change working directory to legacy file's directory
        chdir($legacyFileDir);

        // Set up $_SERVER variables for legacy context
        $_SERVER['SCRIPT_NAME'] = '/' . $requestUri;
        $_SERVER['SCRIPT_FILENAME'] = $legacyPath;
        $_SERVER['PHP_SELF'] = '/' . $requestUri;
        $_SERVER['DOCUMENT_ROOT'] = $legacyRoot;

        // Ensure all HTTP method variables are properly set
        $httpMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        $_SERVER['REQUEST_METHOD'] = $httpMethod;

        // Handle POST data and other HTTP methods
        if ($httpMethod === 'POST') {
            // POST data is automatically available in $_POST
            // Raw POST data is available via php://input
        } elseif (in_array($httpMethod, ['PUT', 'PATCH', 'DELETE'])) {
            // For PUT, PATCH, DELETE, parse the input data
            $rawInput = file_get_contents('php://input');
            if (!empty($rawInput)) {
                // Try to parse as form data
                if (strpos($_SERVER['CONTENT_TYPE'] ?? '', 'application/x-www-form-urlencoded') !== false) {
                    parse_str($rawInput, $_POST);
                }
            }
        }

        // Set content type for PHP execution
        if (!headers_sent()) {
            header('Content-Type: text/html; charset=UTF-8');
        }

        // Execute the legacy PHP file
        include $legacyPath;

    } finally {
        // Restore original environment
        chdir($originalEnv['cwd']);
        $_SERVER['SCRIPT_NAME'] = $originalEnv['script_name'];
        $_SERVER['SCRIPT_FILENAME'] = $originalEnv['script_filename'];
        $_SERVER['PHP_SELF'] = $originalEnv['php_self'];
        $_SERVER['DOCUMENT_ROOT'] = $originalEnv['document_root'];
        $_SERVER['REQUEST_URI'] = $originalEnv['request_uri'];
    }

    exit;
}

/**
 * Serve static files with correct MIME types
 */
function serveStaticFile($filePath) {
    // Get the correct MIME type
    $mimeType = getMimeType($filePath);

    // Set appropriate headers
    header('Content-Type: ' . $mimeType);
    header('Content-Length: ' . filesize($filePath));

    // Set cache headers for static files
    $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
    $cacheableExtensions = ['css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'ico', 'svg', 'woff', 'woff2', 'ttf', 'eot'];

    if (in_array($extension, $cacheableExtensions)) {
        // Cache for 1 hour in development, longer in production
        $cacheTime = 3600; // 1 hour
        header('Cache-Control: public, max-age=' . $cacheTime);
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $cacheTime) . ' GMT');

        // Add ETag for better caching
        $etag = md5_file($filePath);
        header('ETag: "' . $etag . '"');

        // Check if client has cached version
        if (isset($_SERVER['HTTP_IF_NONE_MATCH']) && $_SERVER['HTTP_IF_NONE_MATCH'] === '"' . $etag . '"') {
            http_response_code(304);
            exit;
        }
    }

    // Output the file
    readfile($filePath);
    exit;
}

// If no legacy file found, route to Laravel's public/index.php
if (file_exists(__DIR__ . '/public/index.php')) {
    // Store original environment for Laravel
    $originalDir = getcwd();
    $originalScriptName = $_SERVER['SCRIPT_NAME'] ?? '';
    $originalScriptFilename = $_SERVER['SCRIPT_FILENAME'] ?? '';

    try {
        // Set up the environment for Laravel
        $_SERVER['SCRIPT_NAME'] = '/index.php';
        $_SERVER['SCRIPT_FILENAME'] = __DIR__ . '/public/index.php';

        // Change to public directory
        chdir(__DIR__ . '/public');

        // Include Laravel's front controller
        include __DIR__ . '/public/index.php';

    } finally {
        // Restore environment (though this may not be reached)
        chdir($originalDir);
        $_SERVER['SCRIPT_NAME'] = $originalScriptName;
        $_SERVER['SCRIPT_FILENAME'] = $originalScriptFilename;
    }
} else {
    // Fallback 404
    http_response_code(404);
    header('Content-Type: application/json');
    echo json_encode([
        'error' => 'File not found',
        'requested' => $requestUri,
        'message' => 'Neither legacy file nor Laravel route found',
        'method' => $_SERVER['REQUEST_METHOD'] ?? 'GET',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
